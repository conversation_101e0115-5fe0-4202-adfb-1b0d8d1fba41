// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('性能测试套件', () => {
  
  test.beforeEach(async ({ page }) => {
    test.setTimeout(60000);
  });

  test('页面加载性能测试', async ({ page }) => {
    console.log('⚡ 开始页面加载性能测试');

    // 测试首页加载性能
    const homeStartTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const homeLoadTime = Date.now() - homeStartTime;
    
    console.log(`🏠 首页加载时间: ${homeLoadTime}ms`);
    expect(homeLoadTime).toBeLessThan(5000);

    // 测试管理后台加载性能
    const adminStartTime = Date.now();
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
    const adminLoadTime = Date.now() - adminStartTime;
    
    console.log(`⚙️ 管理后台加载时间: ${adminLoadTime}ms`);
    expect(adminLoadTime).toBeLessThan(5000);

    console.log('✅ 页面加载性能测试通过');
  });

  test('API响应性能测试', async ({ page }) => {
    console.log('🔌 开始API响应性能测试');

    await page.goto('/admin');
    await page.waitForLoadState('networkidle');

    // 初始化数据库
    await page.click('button:has-text("初始化数据库")');
    await page.waitForTimeout(3000);

    // 测试获取学科列表API性能
    const listStartTime = Date.now();
    await page.reload();
    await page.waitForLoadState('networkidle');
    const listResponseTime = Date.now() - listStartTime;
    
    console.log(`📋 学科列表API响应时间: ${listResponseTime}ms`);
    expect(listResponseTime).toBeLessThan(3000);

    // 测试创建学科API性能
    const createStartTime = Date.now();
    await page.click('button:has-text("新建学科")');
    await page.fill('input[placeholder="请输入学科名称"]', '性能测试学科');
    await page.click('.ant-modal button:has-text("创建")');
    await page.waitForTimeout(100);
    const createResponseTime = Date.now() - createStartTime;
    
    console.log(`➕ 创建学科API响应时间: ${createResponseTime}ms`);
    expect(createResponseTime).toBeLessThan(3000);

    // 测试名称检查API性能
    await page.click('button:has-text("新建学科")');
    const checkStartTime = Date.now();
    await page.fill('input[placeholder="请输入学科名称"]', '名称检查测试');
    await page.locator('input[placeholder="请输入学科名称"]').blur();
    await page.waitForTimeout(500);
    const checkResponseTime = Date.now() - checkStartTime;
    
    console.log(`✔️ 名称检查API响应时间: ${checkResponseTime}ms`);
    expect(checkResponseTime).toBeLessThan(2000);

    console.log('✅ API响应性能测试通过');
  });

  test('大量数据性能测试', async ({ page }) => {
    console.log('📊 开始大量数据性能测试');

    await page.goto('/admin');
    await page.waitForLoadState('networkidle');

    // 初始化数据库
    await page.click('button:has-text("初始化数据库")');
    await page.waitForTimeout(3000);

    // 创建多个学科来测试大量数据的性能
    const createCount = 10;
    const createStartTime = Date.now();

    for (let i = 1; i <= createCount; i++) {
      await page.click('button:has-text("新建学科")');
      await page.fill('input[placeholder="请输入学科名称"]', `批量测试学科${i}`);
      await page.fill('textarea[placeholder="请输入学科描述（可选）"]', `这是第${i}个批量测试学科的描述`);
      await page.click('.ant-modal button:has-text("创建")');
      await page.waitForTimeout(500);
    }

    const totalCreateTime = Date.now() - createStartTime;
    const avgCreateTime = totalCreateTime / createCount;
    
    console.log(`📈 创建${createCount}个学科总时间: ${totalCreateTime}ms`);
    console.log(`📈 平均创建时间: ${avgCreateTime}ms`);
    expect(avgCreateTime).toBeLessThan(2000);

    // 测试大量数据下的页面渲染性能
    const renderStartTime = Date.now();
    await page.reload();
    await page.waitForLoadState('networkidle');
    const renderTime = Date.now() - renderStartTime;
    
    console.log(`🎨 大量数据渲染时间: ${renderTime}ms`);
    expect(renderTime).toBeLessThan(5000);

    // 测试视图切换性能
    const switchStartTime = Date.now();
    await page.click('input[value="table"]');
    await page.waitForTimeout(500);
    await page.click('input[value="card"]');
    await page.waitForTimeout(500);
    const switchTime = Date.now() - switchStartTime;
    
    console.log(`🔄 视图切换时间: ${switchTime}ms`);
    expect(switchTime).toBeLessThan(2000);

    console.log('✅ 大量数据性能测试通过');
  });

  test('内存使用性能测试', async ({ page, context }) => {
    console.log('🧠 开始内存使用性能测试');

    await page.goto('/admin');
    await page.waitForLoadState('networkidle');

    // 获取初始内存使用情况
    const initialMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: performance.memory?.usedJSHeapSize || 0,
        totalJSHeapSize: performance.memory?.totalJSHeapSize || 0
      };
    });

    console.log(`🔍 初始内存使用: ${Math.round(initialMetrics.usedJSHeapSize / 1024 / 1024)}MB`);

    // 执行一系列操作
    await page.click('button:has-text("初始化数据库")');
    await page.waitForTimeout(3000);

    // 创建和删除学科
    for (let i = 1; i <= 5; i++) {
      await page.click('button:has-text("新建学科")');
      await page.fill('input[placeholder="请输入学科名称"]', `内存测试学科${i}`);
      await page.click('.ant-modal button:has-text("创建")');
      await page.waitForTimeout(500);
    }

    // 切换视图
    await page.click('input[value="table"]');
    await page.waitForTimeout(500);
    await page.click('input[value="card"]');
    await page.waitForTimeout(500);

    // 获取操作后的内存使用情况
    const finalMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: performance.memory?.usedJSHeapSize || 0,
        totalJSHeapSize: performance.memory?.totalJSHeapSize || 0
      };
    });

    console.log(`🔍 最终内存使用: ${Math.round(finalMetrics.usedJSHeapSize / 1024 / 1024)}MB`);

    const memoryIncrease = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
    const memoryIncreaseMB = Math.round(memoryIncrease / 1024 / 1024);
    
    console.log(`📈 内存增长: ${memoryIncreaseMB}MB`);
    
    // 内存增长应该在合理范围内（小于50MB）
    expect(memoryIncreaseMB).toBeLessThan(50);

    console.log('✅ 内存使用性能测试通过');
  });

  test('网络请求性能测试', async ({ page }) => {
    console.log('🌐 开始网络请求性能测试');

    // 监听网络请求
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          startTime: Date.now()
        });
      }
    });

    const responses = [];
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        responses.push({
          url: response.url(),
          status: response.status(),
          endTime: Date.now()
        });
      }
    });

    await page.goto('/admin');
    await page.waitForLoadState('networkidle');

    // 初始化数据库
    await page.click('button:has-text("初始化数据库")');
    await page.waitForTimeout(3000);

    // 执行一些API操作
    await page.click('button:has-text("新建学科")');
    await page.fill('input[placeholder="请输入学科名称"]', '网络测试学科');
    await page.locator('input[placeholder="请输入学科名称"]').blur();
    await page.waitForTimeout(1000);
    await page.click('.ant-modal button:has-text("创建")');
    await page.waitForTimeout(2000);

    // 分析网络请求性能
    const apiRequests = requests.filter(req => req.url.includes('/api/v1/'));
    const apiResponses = responses.filter(res => res.url.includes('/api/v1/'));

    console.log(`📊 API请求数量: ${apiRequests.length}`);
    console.log(`📊 API响应数量: ${apiResponses.length}`);

    // 计算平均响应时间
    let totalResponseTime = 0;
    let responseCount = 0;

    for (const request of apiRequests) {
      const response = apiResponses.find(res => res.url === request.url);
      if (response) {
        const responseTime = response.endTime - request.startTime;
        totalResponseTime += responseTime;
        responseCount++;
        console.log(`🔗 ${request.method} ${request.url}: ${responseTime}ms`);
      }
    }

    if (responseCount > 0) {
      const avgResponseTime = totalResponseTime / responseCount;
      console.log(`📊 平均API响应时间: ${Math.round(avgResponseTime)}ms`);
      expect(avgResponseTime).toBeLessThan(3000);
    }

    // 验证所有API请求都成功
    const failedResponses = apiResponses.filter(res => res.status >= 400);
    expect(failedResponses.length).toBe(0);

    console.log('✅ 网络请求性能测试通过');
  });
});
