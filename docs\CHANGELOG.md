# 更新日志

本文档记录期末复习平台项目的所有重要变更。

## 版本规范
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

## [未发布]

### 新增
- 待规划功能

### 变更
- 无

### 修复
- 无

### 移除
- 无

---

## [1.1.0] - 2025-08-07 - Sprint 1 完整交付

### 新增
#### 后端开发 ✅
- **Node.js + Koa2 后端服务器**
  - RESTful API架构设计
  - SQLite数据库集成
  - 学科管理CRUD操作
  - 健康检查端点
  - 数据库初始化功能
  - 完整的错误处理机制

#### 前端开发 ✅
- **Vue3 + TypeScript 前端应用**
  - 现代化组件化架构
  - Ant Design Vue UI组件库
  - 响应式页面设计
  - 首页学科展示功能
  - 管理后台CRUD界面
  - API服务层封装

#### 系统集成 ✅
- **前后端联调**
  - Vite代理配置 (/api -> localhost:3000)
  - 并发启动脚本 (concurrently)
  - 完整的数据流验证

#### 测试体系 ✅
- **单元测试**
  - 后端API测试 (Jest)
  - 前端组件测试 (Vitest + Vue Test Utils)
  - 测试覆盖率: 后端100%, 前端68.75%
- **端到端测试**
  - Playwright E2E测试框架
  - 核心用户流程验证
  - 性能指标监控
  - 自动化测试报告

#### 文档体系 ✅
- **技术文档**
  - API接口文档 (完整的接口规范)
  - 后端架构设计文档
  - 前端开发指南
  - 用户交互清单
- **项目管理文档**
  - Sprint计划文档
  - 任务管理系统
  - 变更日志维护

### 变更
- 更新项目结构为前后端分离架构
- 采用现代化技术栈 (Vue3, TypeScript, Koa2)
- 实施TDD开发模式和API契约驱动开发

### 修复
- 解决E2E测试中的选择器匹配问题
- 修复前端组件测试中的Ant Design Vue组件注册问题
- 优化API错误处理和用户反馈机制

### 移除
- 移除初始化时的占位符内容

---

## [1.0.0] - 2025-01-08

### 新增
- 项目初始化
- 创建标准化文档目录结构
  - `/docs/prd/` - 产品需求文档目录
  - `/docs/architecture/` - 架构设计文档目录
  - `/docs/development/` - 开发技术文档目录
  - `/docs/analytics/` - 数据分析文档目录
  - `/docs/tasks/` - 任务规划文档目录
  - `/docs/templates/` - 文档模板库目录
- 创建核心活文档模板
  - `API_Reference.md` - API接口文档模板
  - `Backend_Architecture_and_Guide.md` - 后端架构设计与开发指南模板
  - `Frontend_Development_Guide.md` - 前端开发指南模板
  - `CHANGELOG.md` - 更新日志文档

### 变更
- 无

### 修复
- 无

### 移除
- 无

---

## 版本历史说明

### 版本格式
版本号格式：`主版本号.次版本号.修订号`

### 变更类型
- **新增**: 新功能
- **变更**: 对现有功能的修改
- **修复**: 问题修复
- **移除**: 移除的功能

### 日期格式
使用 ISO 8601 格式：YYYY-MM-DD

---

*本文档由团队成员维护，每次重要变更都应及时更新。*
