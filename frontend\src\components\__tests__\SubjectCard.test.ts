import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import SubjectCard from '../SubjectCard.vue'
import type { Subject } from '@/services/api'

// Mock Ant Design Vue components
vi.mock('ant-design-vue', () => ({
  ACard: {
    name: 'ACard',
    template: '<div class="ant-card"><slot name="title"></slot><slot name="extra"></slot><slot></slot></div>'
  },
  ATag: {
    name: 'ATag',
    template: '<span class="ant-tag"><slot></slot></span>'
  },
  ADropdown: {
    name: 'ADropdown',
    template: '<div class="ant-dropdown"><slot></slot><slot name="overlay"></slot></div>'
  },
  AButton: {
    name: 'AButton',
    template: '<button class="ant-btn"><slot name="icon"></slot><slot></slot></button>'
  },
  AMenu: {
    name: 'AMenu',
    template: '<div class="ant-menu"><slot></slot></div>'
  },
  AMenuItem: {
    name: 'AMenuItem',
    template: '<div class="ant-menu-item"><slot name="icon"></slot><slot></slot></div>'
  },
  AMenuDivider: {
    name: 'AMenuDivider',
    template: '<div class="ant-menu-divider"></div>'
  },
  ASpace: {
    name: 'ASpace',
    template: '<div class="ant-space"><slot></slot></div>'
  }
}))

// Mock icons
vi.mock('@ant-design/icons-vue', () => ({
  MoreOutlined: { name: 'MoreOutlined', template: '<span>More</span>' },
  EditOutlined: { name: 'EditOutlined', template: '<span>Edit</span>' },
  DeleteOutlined: { name: 'DeleteOutlined', template: '<span>Delete</span>' },
  CalendarOutlined: { name: 'CalendarOutlined', template: '<span>Calendar</span>' }
}))

describe('SubjectCard', () => {
  const mockSubject: Subject = {
    id: 1,
    name: '数学',
    description: '高等数学、线性代数、概率论等数学相关内容',
    file_count: 5,
    total_size: 1024000,
    created_at: '2025-01-08T10:00:00.000Z',
    updated_at: '2025-01-08T12:00:00.000Z'
  }

  it('应该正确渲染学科信息', () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject
      }
    })

    expect(wrapper.text()).toContain('数学')
    expect(wrapper.text()).toContain('高等数学、线性代数、概率论等数学相关内容')
    expect(wrapper.text()).toContain('5 个文件')
  })

  it('应该显示文件统计信息', () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject,
        showStats: true
      }
    })

    expect(wrapper.text()).toContain('文件数量')
    expect(wrapper.text()).toContain('5')
    expect(wrapper.text()).toContain('总大小')
    expect(wrapper.text()).toContain('1000.0 KB')
  })

  it('应该隐藏统计信息当showStats为false时', () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject,
        showStats: false
      }
    })

    expect(wrapper.text()).not.toContain('文件数量')
    expect(wrapper.text()).not.toContain('总大小')
  })

  it('应该显示空描述提示', () => {
    const subjectWithoutDescription: Subject = {
      ...mockSubject,
      description: undefined
    }

    const wrapper = mount(SubjectCard, {
      props: {
        subject: subjectWithoutDescription
      }
    })

    expect(wrapper.text()).toContain('暂无描述')
  })

  it('应该正确格式化文件大小', () => {
    const testCases = [
      { size: 0, expected: '0 B' },
      { size: 1024, expected: '1.0 KB' },
      { size: 1048576, expected: '1.0 MB' },
      { size: 1073741824, expected: '1.0 GB' }
    ]

    testCases.forEach(({ size, expected }) => {
      const subject: Subject = {
        ...mockSubject,
        total_size: size
      }

      const wrapper = mount(SubjectCard, {
        props: {
          subject,
          showStats: true
        }
      })

      expect(wrapper.text()).toContain(expected)
    })
  })

  it('应该触发click事件', async () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject
      }
    })

    await wrapper.find('.subject-card').trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')?.[0]).toEqual([mockSubject])
  })

  it('应该触发edit事件', async () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject
      }
    })

    // 模拟点击编辑按钮
    const editButton = wrapper.find('.ant-menu-item')
    await editButton.trigger('click')
    
    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')?.[0]).toEqual([mockSubject])
  })

  it('应该应用selected样式', () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject,
        selected: true
      }
    })

    expect(wrapper.find('.subject-card').classes()).toContain('subject-card--selected')
  })

  it('应该正确格式化日期', () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject
      }
    })

    // 检查是否包含日期相关文本
    expect(wrapper.text()).toContain('创建于')
    expect(wrapper.text()).toContain('更新于')
  })

  it('应该处理相同的创建和更新时间', () => {
    const subjectSameTime: Subject = {
      ...mockSubject,
      updated_at: mockSubject.created_at
    }

    const wrapper = mount(SubjectCard, {
      props: {
        subject: subjectSameTime
      }
    })

    expect(wrapper.text()).toContain('创建于')
    expect(wrapper.text()).not.toContain('更新于')
  })

  it('应该阻止事件冒泡在操作按钮上', async () => {
    const wrapper = mount(SubjectCard, {
      props: {
        subject: mockSubject
      }
    })

    const dropdownButton = wrapper.find('.ant-btn')
    const clickEvent = new Event('click', { bubbles: true })
    const stopPropagationSpy = vi.spyOn(clickEvent, 'stopPropagation')
    
    await dropdownButton.element.dispatchEvent(clickEvent)
    
    expect(stopPropagationSpy).toHaveBeenCalled()
  })

  it('应该处理零文件大小', () => {
    const subjectZeroSize: Subject = {
      ...mockSubject,
      total_size: 0
    }

    const wrapper = mount(SubjectCard, {
      props: {
        subject: subjectZeroSize,
        showStats: true
      }
    })

    expect(wrapper.text()).toContain('0 B')
  })

  it('应该处理未定义的统计字段', () => {
    const subjectNoStats: Subject = {
      id: 1,
      name: '测试学科',
      created_at: '2025-01-08T10:00:00.000Z',
      updated_at: '2025-01-08T10:00:00.000Z'
    }

    const wrapper = mount(SubjectCard, {
      props: {
        subject: subjectNoStats,
        showStats: true
      }
    })

    expect(wrapper.text()).toContain('0')
    expect(wrapper.text()).not.toContain('个文件')
  })
})
