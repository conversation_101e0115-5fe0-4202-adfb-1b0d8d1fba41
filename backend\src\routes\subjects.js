const Router = require('koa-router');
const Subject = require('../models/Subject');
const { validateSubject, validateSubjectUpdate } = require('../middleware/validation');
const { subjectController } = require('../controllers/subjectController');

const router = new Router();
const subjectModel = new Subject();

// GET /api/v1/subjects - 获取所有学科列表
router.get('/subjects', subjectController.getAllSubjects.bind(subjectController));

// GET /api/v1/subjects/check-name - 检查学科名称是否可用 (必须在 :id 路由之前)
router.get('/subjects/check-name', subjectController.checkNameAvailability.bind(subjectController));

// GET /api/v1/subjects/:id - 获取指定学科详情
router.get('/subjects/:id', subjectController.getSubjectById.bind(subjectController));

// POST /api/v1/subjects - 创建新学科 (管理员权限)
router.post('/subjects', validateSubject, subjectController.createSubject.bind(subjectController));

// PUT /api/v1/subjects/:id - 更新指定学科 (管理员权限)
router.put('/subjects/:id', validateSubjectUpdate, subjectController.updateSubject.bind(subjectController));

// DELETE /api/v1/subjects/:id - 删除指定学科 (管理员权限)
router.delete('/subjects/:id', subjectController.deleteSubject.bind(subjectController));

module.exports = router;
