<template>
  <div class="admin-panel">
    <a-layout>
      <a-layout-header class="header">
        <div class="header-content">
          <h1 class="title">管理后台</h1>
          <a-space>
            <a-button @click="$router.push('/')">
              返回首页
            </a-button>
            <a-button @click="initDatabase" :loading="initLoading">
              初始化数据库
            </a-button>
          </a-space>
        </div>
      </a-layout-header>
      
      <a-layout-content class="content">
        <div class="content-wrapper">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-card title="学科管理" class="mb-3">
                <template #extra>
                  <a-space>
                    <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
                      <a-radio-button value="card">卡片视图</a-radio-button>
                      <a-radio-button value="table">表格视图</a-radio-button>
                    </a-radio-group>
                    <a-button type="primary" @click="showCreateModal">
                      新建学科
                    </a-button>
                  </a-space>
                </template>

                <!-- 卡片视图 -->
                <div v-if="viewMode === 'card'" class="subjects-grid">
                  <a-spin :spinning="loading">
                    <div v-if="subjects.length === 0 && !loading" class="empty-state">
                      <a-empty description="暂无学科数据">
                        <a-button type="primary" @click="showCreateModal">
                          创建第一个学科
                        </a-button>
                      </a-empty>
                    </div>
                    <div v-else class="subjects-cards">
                      <SubjectCard
                        v-for="subject in subjects"
                        :key="subject.id"
                        :subject="subject"
                        :selected="selectedSubjectId === subject.id"
                        @click="selectSubject"
                        @edit="editSubject"
                        @delete="confirmDeleteSubject"
                      />
                    </div>
                  </a-spin>
                </div>

                <!-- 表格视图 -->
                <a-table
                  v-else
                  :columns="columns"
                  :data-source="subjects"
                  :loading="loading"
                  row-key="id"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'created_at'">
                      {{ formatDate(record.created_at) }}
                    </template>
                    <template v-else-if="column.key === 'file_count'">
                      {{ record.file_count || 0 }}
                    </template>
                    <template v-else-if="column.key === 'total_size'">
                      {{ formatFileSize(record.total_size || 0) }}
                    </template>
                    <template v-else-if="column.key === 'action'">
                      <a-space>
                        <a-button size="small" @click="viewSubject(record.id)">
                          查看
                        </a-button>
                        <a-button size="small" @click="editSubject(record)">
                          编辑
                        </a-button>
                        <a-popconfirm
                          title="确定要删除这个学科吗？"
                          ok-text="确定"
                          cancel-text="取消"
                          @confirm="deleteSubject(record.id)"
                        >
                          <a-button size="small" danger>
                            删除
                          </a-button>
                        </a-popconfirm>
                      </a-space>
                    </template>
                  </template>
                </a-table>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 学科管理模态框 -->
    <SubjectModal
      v-model:open="modalVisible"
      :mode="modalMode"
      :subject="currentSubject"
      @success="handleModalSuccess"
      @cancel="handleModalCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'
import { ApiService, type Subject, type CreateSubjectRequest } from '@/services/api'
import SubjectCard from '@/components/SubjectCard.vue'
import SubjectModal from '@/components/SubjectModal.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const initLoading = ref(false)
const subjects = ref<Subject[]>([])
const viewMode = ref<'card' | 'table'>('card')
const selectedSubjectId = ref<number | null>(null)

// 模态框相关
const modalVisible = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const currentSubject = ref<Subject | undefined>(undefined)

// 兼容性保留
const createModalVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref<FormInstance>()
const createForm = reactive<CreateSubjectRequest>({
  name: '',
  description: ''
})

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入学科名称' },
    { min: 1, max: 50, message: '学科名称长度应在1-50个字符之间' },
    { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/, message: '学科名称只能包含中英文、数字、空格、连字符和下划线' }
  ]
}

// 表格列定义
const columns: TableColumnsType = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '学科名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '文件数量',
    dataIndex: 'file_count',
    key: 'file_count',
    width: 100,
    align: 'center'
  },
  {
    title: '总大小',
    dataIndex: 'total_size',
    key: 'total_size',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 加载学科列表
const loadSubjects = async () => {
  try {
    loading.value = true
    const response = await ApiService.getSubjects()
    
    if (response.success) {
      subjects.value = response.data
    } else {
      message.error(response.message || '加载学科列表失败')
    }
  } catch (error: any) {
    console.error('加载学科列表失败:', error)
    message.error(error.message || '网络错误')
  } finally {
    loading.value = false
  }
}

// 初始化数据库
const initDatabase = async () => {
  try {
    initLoading.value = true
    const response = await ApiService.initDatabase()
    
    if (response.success) {
      message.success('数据库初始化成功')
      await loadSubjects() // 重新加载数据
    } else {
      message.error(response.message || '数据库初始化失败')
    }
  } catch (error: any) {
    console.error('数据库初始化失败:', error)
    message.error(error.message || '网络错误')
  } finally {
    initLoading.value = false
  }
}

// 显示创建模态框
const showCreateModal = () => {
  modalMode.value = 'create'
  currentSubject.value = undefined
  modalVisible.value = true
  // 兼容性保留
  createForm.name = ''
  createForm.description = ''
  createModalVisible.value = true
}

// 选择学科
const selectSubject = (subject: Subject) => {
  selectedSubjectId.value = subject.id
}

// 编辑学科
const editSubject = (subject: Subject) => {
  modalMode.value = 'edit'
  currentSubject.value = subject
  modalVisible.value = true
}

// 确认删除学科
const confirmDeleteSubject = (subject: Subject) => {
  deleteSubject(subject.id)
}

// 处理模态框成功
const handleModalSuccess = (subject: Subject) => {
  loadSubjects() // 重新加载列表
  selectedSubjectId.value = subject.id // 选中新创建或更新的学科
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
}

// 处理创建学科
const handleCreate = async () => {
  try {
    await createFormRef.value?.validate()
    
    createLoading.value = true
    const response = await ApiService.createSubject(createForm)
    
    if (response.success) {
      message.success('学科创建成功')
      createModalVisible.value = false
      await loadSubjects() // 重新加载数据
    } else {
      message.error(response.message || '创建学科失败')
    }
  } catch (error: any) {
    console.error('创建学科失败:', error)
    if (error.message) {
      message.error(error.message)
    }
  } finally {
    createLoading.value = false
  }
}

// 查看学科
const viewSubject = (id: number) => {
  router.push(`/subjects/${id}`)
}

// 删除学科
const deleteSubject = async (id: number) => {
  try {
    const response = await ApiService.deleteSubject(id)
    
    if (response.success) {
      message.success(`学科删除成功，同时删除了 ${response.data.deleted_files_count} 个文件`)
      await loadSubjects() // 重新加载数据
    } else {
      message.error(response.message || '删除学科失败')
    }
  } catch (error: any) {
    console.error('删除学科失败:', error)
    message.error(error.message || '网络错误')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSubjects()
})
</script>

<style scoped>
.admin-panel {
  min-height: 100vh;
}

.header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.title {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.content {
  padding: 24px;
  background: #f5f5f5;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.mb-3 {
  margin-bottom: 24px;
}

/* 学科卡片网格布局 */
.subjects-grid {
  min-height: 200px;
}

.subjects-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .subjects-cards {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .subjects-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .content-wrapper {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-content .title {
    margin: 0;
  }
}
</style>
