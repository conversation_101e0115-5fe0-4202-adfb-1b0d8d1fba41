// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('学科管理完整功能测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 设置更长的超时时间
    test.setTimeout(120000);
    
    // 访问管理后台
    await page.goto('/admin');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 初始化数据库（确保测试环境干净）
    await page.click('button:has-text("初始化数据库")');
    await page.waitForTimeout(3000);
  });

  test('完整的学科管理流程 - 创建、编辑、删除', async ({ page }) => {
    console.log('🧪 开始学科管理完整流程测试');

    // === 第一步：验证管理后台页面加载 ===
    await expect(page.locator('h1:has-text("管理后台")')).toBeVisible();
    await expect(page.locator('.ant-card:has-text("学科管理")')).toBeVisible();
    
    // 验证视图切换按钮
    await expect(page.locator('input[value="card"]')).toBeVisible();
    await expect(page.locator('input[value="table"]')).toBeVisible();
    
    console.log('✅ 管理后台页面加载验证通过');

    // === 第二步：测试创建学科功能 ===
    console.log('📝 测试创建学科功能');
    
    // 点击新建学科按钮
    await page.click('button:has-text("新建学科")');
    
    // 验证弹窗打开
    await expect(page.locator('.ant-modal:has-text("新建学科")')).toBeVisible();
    
    // 填写学科信息
    await page.fill('input[placeholder="请输入学科名称"]', '测试学科A');
    await page.fill('textarea[placeholder="请输入学科描述（可选）"]', '这是一个测试学科的描述');
    
    // 测试名称唯一性验证
    await page.locator('input[placeholder="请输入学科名称"]').blur();
    await page.waitForTimeout(1000);
    
    // 验证名称可用提示
    await expect(page.locator('.ant-alert-success:has-text("可以使用")')).toBeVisible();
    
    // 提交创建
    await page.click('.ant-modal button:has-text("创建")');
    
    // 等待创建成功
    await page.waitForTimeout(2000);
    
    // 验证学科卡片出现（卡片视图）
    await expect(page.locator('.subject-card:has-text("测试学科A")')).toBeVisible();
    
    console.log('✅ 学科创建功能验证通过');

    // === 第三步：测试编辑学科功能 ===
    console.log('✏️ 测试编辑学科功能');
    
    // 点击学科卡片的操作菜单
    await page.locator('.subject-card:has-text("测试学科A") .ant-dropdown-trigger').click();
    
    // 点击编辑选项
    await page.click('.ant-menu-item:has-text("编辑")');
    
    // 验证编辑弹窗打开
    await expect(page.locator('.ant-modal:has-text("编辑学科")')).toBeVisible();
    
    // 验证表单预填充
    await expect(page.locator('input[value="测试学科A"]')).toBeVisible();
    
    // 修改学科信息
    await page.fill('input[placeholder="请输入学科名称"]', '测试学科A-已编辑');
    await page.fill('textarea[placeholder="请输入学科描述（可选）"]', '这是编辑后的学科描述');
    
    // 测试编辑时的名称验证（应该排除当前学科ID）
    await page.locator('input[placeholder="请输入学科名称"]').blur();
    await page.waitForTimeout(1000);
    
    // 提交编辑
    await page.click('.ant-modal button:has-text("保存")');
    
    // 等待编辑成功
    await page.waitForTimeout(2000);
    
    // 验证学科信息已更新
    await expect(page.locator('.subject-card:has-text("测试学科A-已编辑")')).toBeVisible();
    
    console.log('✅ 学科编辑功能验证通过');

    // === 第四步：测试表格视图 ===
    console.log('📊 测试表格视图');
    
    // 切换到表格视图
    await page.click('input[value="table"]');
    
    // 验证表格显示
    await expect(page.locator('.ant-table')).toBeVisible();
    await expect(page.locator('.ant-table td:has-text("测试学科A-已编辑")')).toBeVisible();
    
    // 验证统计字段列
    await expect(page.locator('th:has-text("文件数量")')).toBeVisible();
    await expect(page.locator('th:has-text("总大小")')).toBeVisible();
    
    console.log('✅ 表格视图验证通过');

    // === 第五步：测试删除学科功能 ===
    console.log('🗑️ 测试删除学科功能');
    
    // 切换回卡片视图进行删除测试
    await page.click('input[value="card"]');
    
    // 点击学科卡片的操作菜单
    await page.locator('.subject-card:has-text("测试学科A-已编辑") .ant-dropdown-trigger').click();
    
    // 点击删除选项
    await page.click('.ant-menu-item:has-text("删除")');
    
    // 等待删除完成
    await page.waitForTimeout(2000);
    
    // 验证学科已被删除
    await expect(page.locator('.subject-card:has-text("测试学科A-已编辑")')).not.toBeVisible();
    
    // 验证空状态显示
    await expect(page.locator('.ant-empty')).toBeVisible();
    await expect(page.locator('button:has-text("创建第一个学科")')).toBeVisible();
    
    console.log('✅ 学科删除功能验证通过');
    console.log('🎉 学科管理完整流程测试通过！');
  });

  test('表单验证和错误处理测试', async ({ page }) => {
    console.log('🛡️ 开始表单验证和错误处理测试');

    // 打开创建学科弹窗
    await page.click('button:has-text("新建学科")');
    
    // === 测试必填字段验证 ===
    console.log('📝 测试必填字段验证');
    
    // 尝试提交空表单
    await page.click('.ant-modal button:has-text("创建")');
    
    // 验证验证错误信息
    await expect(page.locator('.ant-form-item-explain-error:has-text("请输入学科名称")')).toBeVisible();
    
    // === 测试字符长度限制 ===
    console.log('📏 测试字符长度限制');
    
    // 输入超长名称
    const longName = 'A'.repeat(60); // 超过50字符限制
    await page.fill('input[placeholder="请输入学科名称"]', longName);
    await page.click('.ant-modal button:has-text("创建")');
    
    // 验证长度限制错误
    await expect(page.locator('.ant-form-item-explain-error:has-text("长度应在1-50个字符之间")')).toBeVisible();
    
    // === 测试名称格式验证 ===
    console.log('🔤 测试名称格式验证');
    
    // 输入包含特殊字符的名称
    await page.fill('input[placeholder="请输入学科名称"]', '测试@#$%学科');
    await page.click('.ant-modal button:has-text("创建")');
    
    // 验证格式错误
    await expect(page.locator('.ant-form-item-explain-error:has-text("只能包含中英文、数字、空格、连字符和下划线")')).toBeVisible();
    
    // === 测试名称重复验证 ===
    console.log('🔄 测试名称重复验证');
    
    // 先创建一个学科
    await page.fill('input[placeholder="请输入学科名称"]', '重复测试学科');
    await page.click('.ant-modal button:has-text("创建")');
    await page.waitForTimeout(2000);
    
    // 再次尝试创建同名学科
    await page.click('button:has-text("新建学科")');
    await page.fill('input[placeholder="请输入学科名称"]', '重复测试学科');
    
    // 失焦触发名称检查
    await page.locator('input[placeholder="请输入学科名称"]').blur();
    await page.waitForTimeout(1000);
    
    // 验证名称不可用提示
    await expect(page.locator('.ant-alert-error:has-text("已存在")')).toBeVisible();
    
    // 尝试提交应该失败
    await page.click('.ant-modal button:has-text("创建")');
    await page.waitForTimeout(1000);
    
    // 弹窗应该仍然存在（提交失败）
    await expect(page.locator('.ant-modal:has-text("新建学科")')).toBeVisible();
    
    console.log('✅ 表单验证和错误处理测试通过');
  });

  test('响应式设计测试', async ({ page }) => {
    console.log('📱 开始响应式设计测试');

    // === 测试桌面视图 ===
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // 验证桌面布局
    await expect(page.locator('.subjects-cards')).toBeVisible();
    
    // === 测试平板视图 ===
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    
    // 验证平板布局调整
    await expect(page.locator('.subjects-cards')).toBeVisible();
    
    // === 测试手机视图 ===
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // 验证手机布局
    await expect(page.locator('.subjects-cards')).toBeVisible();
    
    // 测试手机端的操作
    await page.click('button:has-text("新建学科")');
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 验证弹窗在小屏幕上的显示
    const modal = page.locator('.ant-modal');
    const modalBox = await modal.boundingBox();
    expect(modalBox.width).toBeLessThanOrEqual(375);
    
    console.log('✅ 响应式设计测试通过');
  });

  test('性能测试', async ({ page }) => {
    console.log('⚡ 开始性能测试');

    // 测试页面加载时间
    const startTime = Date.now();
    await page.goto('/admin');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`📊 页面加载时间: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(5000); // 页面加载时间应小于5秒
    
    // 测试API响应时间
    const apiStartTime = Date.now();
    await page.click('button:has-text("新建学科")');
    await page.fill('input[placeholder="请输入学科名称"]', '性能测试学科');
    await page.click('.ant-modal button:has-text("创建")');
    
    // 等待API响应
    await page.waitForTimeout(100);
    const apiResponseTime = Date.now() - apiStartTime;
    
    console.log(`📊 API响应时间: ${apiResponseTime}ms`);
    expect(apiResponseTime).toBeLessThan(3000); // API响应时间应小于3秒
    
    console.log('✅ 性能测试通过');
  });
});
