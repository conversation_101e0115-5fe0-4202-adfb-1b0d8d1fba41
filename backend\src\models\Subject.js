const { getDatabase } = require('../config/database');

class Subject {
  constructor() {
    this.db = getDatabase();
    this.tableName = 'subjects';
  }

  // 获取所有学科
  findAll() {
    try {
      const stmt = this.db.prepare(`
        SELECT id, name, description, file_count, total_size, created_at, updated_at
        FROM ${this.tableName}
        ORDER BY created_at DESC
      `);
      return stmt.all();
    } catch (error) {
      console.error('查询所有学科失败:', error);
      throw error;
    }
  }

  // 根据ID查找学科
  findById(id) {
    try {
      const stmt = this.db.prepare(`
        SELECT id, name, description, file_count, total_size, created_at, updated_at
        FROM ${this.tableName}
        WHERE id = ?
      `);
      return stmt.get(id);
    } catch (error) {
      console.error('根据ID查询学科失败:', error);
      throw error;
    }
  }

  // 根据名称查找学科
  findByName(name) {
    try {
      const stmt = this.db.prepare(`
        SELECT id, name, description, file_count, total_size, created_at, updated_at
        FROM ${this.tableName}
        WHERE name = ?
      `);
      return stmt.get(name);
    } catch (error) {
      console.error('根据名称查询学科失败:', error);
      throw error;
    }
  }

  // 创建新学科
  create(data) {
    try {
      const { name, description = null } = data;

      // 检查名称是否已存在
      const existing = this.findByName(name);
      if (existing) {
        throw new Error('学科名称已存在');
      }

      const stmt = this.db.prepare(`
        INSERT INTO ${this.tableName} (name, description, file_count, total_size, created_at, updated_at)
        VALUES (?, ?, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `);

      const result = stmt.run(name, description);

      // 返回创建的学科信息
      return this.findById(result.lastInsertRowid);
    } catch (error) {
      console.error('创建学科失败:', error);
      throw error;
    }
  }

  // 更新学科
  update(id, data) {
    try {
      const { name, description } = data;

      // 检查学科是否存在
      const existing = this.findById(id);
      if (!existing) {
        throw new Error('学科不存在');
      }

      // 如果更新名称，检查新名称是否已被其他学科使用
      if (name && name !== existing.name) {
        const nameExists = this.findByName(name);
        if (nameExists) {
          throw new Error('学科名称已存在');
        }
      }

      const stmt = this.db.prepare(`
        UPDATE ${this.tableName} 
        SET name = COALESCE(?, name), 
            description = COALESCE(?, description),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      const result = stmt.run(name, description, id);

      if (result.changes === 0) {
        throw new Error('更新失败，学科不存在');
      }

      // 返回更新后的学科信息
      return this.findById(id);
    } catch (error) {
      console.error('更新学科失败:', error);
      throw error;
    }
  }

  // 删除学科
  delete(id) {
    try {
      // 检查学科是否存在
      const existing = this.findById(id);
      if (!existing) {
        throw new Error('学科不存在');
      }

      // 开始事务，同时删除相关的文件节点
      const transaction = this.db.transaction(() => {
        // 删除相关的文件节点
        const deleteFilesStmt = this.db.prepare('DELETE FROM file_nodes WHERE subject_id = ?');
        const filesResult = deleteFilesStmt.run(id);

        // 删除学科
        const deleteSubjectStmt = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`);
        const subjectResult = deleteSubjectStmt.run(id);

        return {
          deleted_id: id,
          deleted_files_count: filesResult.changes
        };
      });

      return transaction();
    } catch (error) {
      console.error('删除学科失败:', error);
      throw error;
    }
  }

  // 获取学科统计信息
  getStats(id) {
    try {
      const stmt = this.db.prepare(`
        SELECT 
          s.id,
          s.name,
          s.description,
          s.created_at,
          s.updated_at,
          COUNT(fn.id) as file_count,
          SUM(CASE WHEN fn.type = 'file' THEN 1 ELSE 0 END) as file_count_only,
          SUM(CASE WHEN fn.type = 'folder' THEN 1 ELSE 0 END) as folder_count,
          SUM(fn.file_size) as total_size
        FROM ${this.tableName} s
        LEFT JOIN file_nodes fn ON s.id = fn.subject_id
        WHERE s.id = ?
        GROUP BY s.id
      `);

      return stmt.get(id);
    } catch (error) {
      console.error('获取学科统计信息失败:', error);
      throw error;
    }
  }

  // 检查学科名称是否可用
  isNameAvailable(name, excludeId = null) {
    try {
      let stmt;
      let params;

      if (excludeId) {
        stmt = this.db.prepare(`SELECT id FROM ${this.tableName} WHERE name = ? AND id != ?`);
        params = [name, excludeId];
      } else {
        stmt = this.db.prepare(`SELECT id FROM ${this.tableName} WHERE name = ?`);
        params = [name];
      }

      const result = stmt.get(...params);
      return !result; // 如果没有找到记录，说明名称可用
    } catch (error) {
      console.error('检查学科名称可用性失败:', error);
      throw error;
    }
  }

  // 更新学科统计信息
  updateStats(id) {
    try {
      // 检查学科是否存在
      const existing = this.findById(id);
      if (!existing) {
        throw new Error('学科不存在');
      }

      // 计算统计信息
      const statsStmt = this.db.prepare(`
        SELECT
          COUNT(fn.id) as file_count,
          SUM(CASE WHEN fn.type = 'file' THEN fn.file_size ELSE 0 END) as total_size
        FROM file_nodes fn
        WHERE fn.subject_id = ?
      `);

      const stats = statsStmt.get(id);
      const fileCount = stats.file_count || 0;
      const totalSize = stats.total_size || 0;

      // 更新学科统计信息
      const updateStmt = this.db.prepare(`
        UPDATE ${this.tableName}
        SET file_count = ?, total_size = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      const result = updateStmt.run(fileCount, totalSize, id);

      if (result.changes === 0) {
        throw new Error('更新统计信息失败，学科不存在');
      }

      // 返回更新后的学科信息
      return this.findById(id);
    } catch (error) {
      console.error('更新学科统计信息失败:', error);
      throw error;
    }
  }

  // 批量更新所有学科的统计信息
  updateAllStats() {
    try {
      const allSubjects = this.findAll();
      const updatedSubjects = [];

      for (const subject of allSubjects) {
        const updated = this.updateStats(subject.id);
        updatedSubjects.push(updated);
      }

      return updatedSubjects;
    } catch (error) {
      console.error('批量更新学科统计信息失败:', error);
      throw error;
    }
  }

  // 获取学科详细信息（包含实时统计）
  findByIdWithStats(id) {
    try {
      // 先更新统计信息
      this.updateStats(id);
      // 返回最新的学科信息
      return this.findById(id);
    } catch (error) {
      console.error('获取学科详细信息失败:', error);
      throw error;
    }
  }

  // 获取所有学科（包含实时统计）
  findAllWithStats() {
    try {
      // 批量更新统计信息
      return this.updateAllStats();
    } catch (error) {
      console.error('获取所有学科统计信息失败:', error);
      throw error;
    }
  }
}

module.exports = Subject;
