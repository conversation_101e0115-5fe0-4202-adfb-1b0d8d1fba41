<testsuites id="" name="" tests="133" failures="32" skipped="85" errors="0" time="126.29213699999998">
<testsuite name="basic-flow.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="chromium" tests="7" failures="6" skipped="0" time="57.897" errors="0">
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表" classname="basic-flow.spec.js" time="6.583">
<failure message="basic-flow.spec.js:11:3 用户可以访问首页并查看学科列表" type="FAILURE">
<![CDATA[  [chromium] › basic-flow.spec.js:11:3 › 期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表 ─────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.ant-card-extra button:has-text("刷新")')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.ant-card-extra button:has-text("刷新")')


      29 |
      30 |     // 验证刷新按钮（在卡片的extra区域）
    > 31 |     await expect(page.locator('.ant-card-extra button:has-text("刷新")')).toBeVisible();
         |                                                                         ^
      32 |
      33 |     console.log('✅ 首页访问和基本结构验证通过');
      34 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:31:73

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以执行健康检查" classname="basic-flow.spec.js" time="3.462">
<system-out>
<![CDATA[✅ 健康检查功能验证通过
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问管理后台" classname="basic-flow.spec.js" time="6.659">
<failure message="basic-flow.spec.js:52:3 用户可以访问管理后台" type="FAILURE">
<![CDATA[  [chromium] › basic-flow.spec.js:52:3 › 期末复习平台 - 基础功能流程 › 用户可以访问管理后台 ──────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.ant-table')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.ant-table')


      65 |     await expect(page.locator('button:has-text("新建学科")')).toBeVisible();
      66 |     await expect(page.locator('button:has-text("初始化数据库")')).toBeVisible();
    > 67 |     await expect(page.locator('.ant-table')).toBeVisible();
         |                                              ^
      68 |
      69 |     console.log('✅ 管理后台访问验证通过');
      70 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:67:46

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以创建新学科" classname="basic-flow.spec.js" time="9.535">
<failure message="basic-flow.spec.js:72:3 用户可以创建新学科" type="FAILURE">
<![CDATA[  [chromium] › basic-flow.spec.js:72:3 › 期末复习平台 - 基础功能流程 › 用户可以创建新学科 ───────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('.ant-table-tbody')
    Expected string: "测试学科_1754533640052"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('.ant-table-tbody')


       95 |
       96 |     // 验证学科出现在列表中
    >  97 |     await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);
          |                                                    ^
       98 |
       99 |     console.log('✅ 创建学科功能验证通过');
      100 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:97:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以删除学科" classname="basic-flow.spec.js" time="9.363">
<failure message="basic-flow.spec.js:102:3 用户可以删除学科" type="FAILURE">
<![CDATA[  [chromium] › basic-flow.spec.js:102:3 › 期末复习平台 - 基础功能流程 › 用户可以删除学科 ───────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('.ant-table-tbody')
    Expected string: "待删除学科_1754533639928"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('.ant-table-tbody')


      114 |
      115 |     // 验证学科已创建
    > 116 |     await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);
          |                                                    ^
      117 |
      118 |     // 找到删除按钮并点击
      119 |     const deleteButton = page.locator(`tr:has-text("${subjectName}") button:has-text("删除")`);
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:116:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" classname="basic-flow.spec.js" time="15.102">
<failure message="basic-flow.spec.js:139:3 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" type="FAILURE">
<![CDATA[  [chromium] › basic-flow.spec.js:139:3 › 期末复习平台 - 基础功能流程 › 完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页 ──────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-card-extra button:has-text("刷新")')


      159 |
      160 |     // 5. 刷新首页数据
    > 161 |     await page.click('.ant-card-extra button:has-text("刷新")');
          |                ^
      162 |     await page.waitForTimeout(2000);
      163 |
      164 |     // 6. 验证新创建的学科出现在首页
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:161:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › API响应时间性能测试" classname="basic-flow.spec.js" time="7.193">
<failure message="basic-flow.spec.js:170:3 API响应时间性能测试" type="FAILURE">
<![CDATA[  [chromium] › basic-flow.spec.js:170:3 › 期末复习平台 - 基础功能流程 › API响应时间性能测试 ────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.
    Call log:
      - waiting for locator('.ant-table-tbody') to be visible


      183 |     await page.goto('/admin');
      184 |     const listStartTime = Date.now();
    > 185 |     await page.waitForSelector('.ant-table-tbody', { timeout: 5000 });
          |                ^
      186 |     const listLoadTime = Date.now() - listStartTime;
      187 |
      188 |     // 验证列表加载时间小于5秒
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:185:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="performance.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="chromium" tests="5" failures="4" skipped="0" time="63.669" errors="0">
<testcase name="性能测试套件 › 页面加载性能测试" classname="performance.spec.js" time="2.132">
<system-out>
<![CDATA[⚡ 开始页面加载性能测试
🏠 首页加载时间: 1046ms
⚙️ 管理后台加载时间: 856ms
✅ 页面加载性能测试通过
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › API响应性能测试" classname="performance.spec.js" time="15.663">
<failure message="performance.spec.js:34:3 API响应性能测试" type="FAILURE">
<![CDATA[  [chromium] › performance.spec.js:34:3 › 性能测试套件 › API响应性能测试 ───────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      55 |     await page.click('button:has-text("新建学科")');
      56 |     await page.fill('input[placeholder="请输入学科名称"]', '性能测试学科');
    > 57 |     await page.click('.ant-modal button:has-text("创建")');
         |                ^
      58 |     await page.waitForTimeout(100);
      59 |     const createResponseTime = Date.now() - createStartTime;
      60 |     
        at D:\ai\Term Review\tests\e2e\performance.spec.js:57:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-API响应性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-API响应性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-API响应性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔌 开始API响应性能测试
📋 学科列表API响应时间: 874ms

[[ATTACHMENT|performance-性能测试套件-API响应性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-API响应性能测试-chromium\video.webm]]

[[ATTACHMENT|performance-性能测试套件-API响应性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › 大量数据性能测试" classname="performance.spec.js" time="14.775">
<failure message="performance.spec.js:78:3 大量数据性能测试" type="FAILURE">
<![CDATA[  [chromium] › performance.spec.js:78:3 › 性能测试套件 › 大量数据性能测试 ────────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      94 |       await page.fill('input[placeholder="请输入学科名称"]', `批量测试学科${i}`);
      95 |       await page.fill('textarea[placeholder="请输入学科描述（可选）"]', `这是第${i}个批量测试学科的描述`);
    > 96 |       await page.click('.ant-modal button:has-text("创建")');
         |                  ^
      97 |       await page.waitForTimeout(500);
      98 |     }
      99 |
        at D:\ai\Term Review\tests\e2e\performance.spec.js:96:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-大量数据性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-大量数据性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-大量数据性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[📊 开始大量数据性能测试

[[ATTACHMENT|performance-性能测试套件-大量数据性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-大量数据性能测试-chromium\video.webm]]

[[ATTACHMENT|performance-性能测试套件-大量数据性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › 内存使用性能测试" classname="performance.spec.js" time="14.875">
<failure message="performance.spec.js:130:3 内存使用性能测试" type="FAILURE">
<![CDATA[  [chromium] › performance.spec.js:130:3 › 性能测试套件 › 内存使用性能测试 ───────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      152 |       await page.click('button:has-text("新建学科")');
      153 |       await page.fill('input[placeholder="请输入学科名称"]', `内存测试学科${i}`);
    > 154 |       await page.click('.ant-modal button:has-text("创建")');
          |                  ^
      155 |       await page.waitForTimeout(500);
      156 |     }
      157 |
        at D:\ai\Term Review\tests\e2e\performance.spec.js:154:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-内存使用性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-内存使用性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-内存使用性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🧠 开始内存使用性能测试
🔍 初始内存使用: 18MB

[[ATTACHMENT|performance-性能测试套件-内存使用性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-内存使用性能测试-chromium\video.webm]]

[[ATTACHMENT|performance-性能测试套件-内存使用性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › 网络请求性能测试" classname="performance.spec.js" time="16.224">
<failure message="performance.spec.js:185:3 网络请求性能测试" type="FAILURE">
<![CDATA[  [chromium] › performance.spec.js:185:3 › 性能测试套件 › 网络请求性能测试 ───────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      221 |     await page.locator('input[placeholder="请输入学科名称"]').blur();
      222 |     await page.waitForTimeout(1000);
    > 223 |     await page.click('.ant-modal button:has-text("创建")');
          |                ^
      224 |     await page.waitForTimeout(2000);
      225 |
      226 |     // 分析网络请求性能
        at D:\ai\Term Review\tests\e2e\performance.spec.js:223:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-网络请求性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-网络请求性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-网络请求性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🌐 开始网络请求性能测试

[[ATTACHMENT|performance-性能测试套件-网络请求性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-网络请求性能测试-chromium\video.webm]]

[[ATTACHMENT|performance-性能测试套件-网络请求性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="simple-test.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="chromium" tests="3" failures="0" skipped="0" time="6.052" errors="0">
<testcase name="简单功能验证 › 验证前后端连接正常" classname="simple-test.spec.js" time="3.059">
<system-out>
<![CDATA[✅ 前后端连接验证通过
]]>
</system-out>
</testcase>
<testcase name="简单功能验证 › 验证管理后台访问" classname="simple-test.spec.js" time="1.085">
<system-out>
<![CDATA[✅ 管理后台访问验证通过
]]>
</system-out>
</testcase>
<testcase name="简单功能验证 › 验证API响应性能" classname="simple-test.spec.js" time="1.908">
<system-out>
<![CDATA[✅ API响应性能验证通过 - 响应时间: 1422ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="subject-management.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="chromium" tests="4" failures="3" skipped="0" time="48.424" errors="0">
<testcase name="学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除" classname="subject-management.spec.js" time="9.768">
<failure message="subject-management.spec.js:21:3 完整的学科管理流程 - 创建、编辑、删除" type="FAILURE">
<![CDATA[  [chromium] › subject-management.spec.js:21:3 › 学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除 ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('input[value="card"]')
    Expected: visible
    Received: hidden
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('input[value="card"]')
        9 × locator resolved to <input checked type="radio" value="card" class="ant-radio-button-input"/>
          - unexpected value "hidden"


      27 |     
      28 |     // 验证视图切换按钮
    > 29 |     await expect(page.locator('input[value="card"]')).toBeVisible();
         |                                                       ^
      30 |     await expect(page.locator('input[value="table"]')).toBeVisible();
      31 |     
      32 |     console.log('✅ 管理后台页面加载验证通过');
        at D:\ai\Term Review\tests\e2e\subject-management.spec.js:29:55

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🧪 开始学科管理完整流程测试

[[ATTACHMENT|subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\test-failed-1.png]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\video.webm]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="学科管理完整功能测试 › 表单验证和错误处理测试" classname="subject-management.spec.js" time="15.125">
<failure message="subject-management.spec.js:141:3 表单验证和错误处理测试" type="FAILURE">
<![CDATA[  [chromium] › subject-management.spec.js:141:3 › 学科管理完整功能测试 › 表单验证和错误处理测试 ─────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      149 |     
      150 |     // 尝试提交空表单
    > 151 |     await page.click('.ant-modal button:has-text("创建")');
          |                ^
      152 |     
      153 |     // 验证验证错误信息
      154 |     await expect(page.locator('.ant-form-item-explain-error:has-text("请输入学科名称")')).toBeVisible();
        at D:\ai\Term Review\tests\e2e\subject-management.spec.js:151:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🛡️ 开始表单验证和错误处理测试
📝 测试必填字段验证

[[ATTACHMENT|subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\test-failed-1.png]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\video.webm]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="学科管理完整功能测试 › 响应式设计测试" classname="subject-management.spec.js" time="7.766">
<system-out>
<![CDATA[📱 开始响应式设计测试
✅ 响应式设计测试通过
]]>
</system-out>
</testcase>
<testcase name="学科管理完整功能测试 › 性能测试" classname="subject-management.spec.js" time="15.765">
<failure message="subject-management.spec.js:243:3 性能测试" type="FAILURE">
<![CDATA[  [chromium] › subject-management.spec.js:243:3 › 学科管理完整功能测试 › 性能测试 ────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      257 |     await page.click('button:has-text("新建学科")');
      258 |     await page.fill('input[placeholder="请输入学科名称"]', '性能测试学科');
    > 259 |     await page.click('.ant-modal button:has-text("创建")');
          |                ^
      260 |     
      261 |     // 等待API响应
      262 |     await page.waitForTimeout(100);
        at D:\ai\Term Review\tests\e2e\subject-management.spec.js:259:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-性能测试-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-性能测试-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\subject-management-学科管理完整功能测试-性能测试-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[⚡ 开始性能测试
📊 页面加载时间: 1001ms

[[ATTACHMENT|subject-management-学科管理完整功能测试-性能测试-chromium\test-failed-1.png]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-性能测试-chromium\video.webm]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-性能测试-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="basic-flow.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="firefox" tests="7" failures="6" skipped="0" time="67.351" errors="0">
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表" classname="basic-flow.spec.js" time="7.751">
<failure message="basic-flow.spec.js:11:3 用户可以访问首页并查看学科列表" type="FAILURE">
<![CDATA[  [firefox] › basic-flow.spec.js:11:3 › 期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表 ──────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.ant-card-extra button:has-text("刷新")')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.ant-card-extra button:has-text("刷新")')


      29 |
      30 |     // 验证刷新按钮（在卡片的extra区域）
    > 31 |     await expect(page.locator('.ant-card-extra button:has-text("刷新")')).toBeVisible();
         |                                                                         ^
      32 |
      33 |     console.log('✅ 首页访问和基本结构验证通过');
      34 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:31:73

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以执行健康检查" classname="basic-flow.spec.js" time="5.559">
<system-out>
<![CDATA[✅ 健康检查功能验证通过
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问管理后台" classname="basic-flow.spec.js" time="8.753">
<failure message="basic-flow.spec.js:52:3 用户可以访问管理后台" type="FAILURE">
<![CDATA[  [firefox] › basic-flow.spec.js:52:3 › 期末复习平台 - 基础功能流程 › 用户可以访问管理后台 ───────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.ant-table')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.ant-table')


      65 |     await expect(page.locator('button:has-text("新建学科")')).toBeVisible();
      66 |     await expect(page.locator('button:has-text("初始化数据库")')).toBeVisible();
    > 67 |     await expect(page.locator('.ant-table')).toBeVisible();
         |                                              ^
      68 |
      69 |     console.log('✅ 管理后台访问验证通过');
      70 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:67:46

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以创建新学科" classname="basic-flow.spec.js" time="10.848">
<failure message="basic-flow.spec.js:72:3 用户可以创建新学科" type="FAILURE">
<![CDATA[  [firefox] › basic-flow.spec.js:72:3 › 期末复习平台 - 基础功能流程 › 用户可以创建新学科 ────────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('.ant-table-tbody')
    Expected string: "测试学科_1754533678904"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('.ant-table-tbody')


       95 |
       96 |     // 验证学科出现在列表中
    >  97 |     await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);
          |                                                    ^
       98 |
       99 |     console.log('✅ 创建学科功能验证通过');
      100 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:97:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以删除学科" classname="basic-flow.spec.js" time="10.568">
<failure message="basic-flow.spec.js:102:3 用户可以删除学科" type="FAILURE">
<![CDATA[  [firefox] › basic-flow.spec.js:102:3 › 期末复习平台 - 基础功能流程 › 用户可以删除学科 ────────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('.ant-table-tbody')
    Expected string: "待删除学科_1754533677927"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('.ant-table-tbody')


      114 |
      115 |     // 验证学科已创建
    > 116 |     await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);
          |                                                    ^
      117 |
      118 |     // 找到删除按钮并点击
      119 |     const deleteButton = page.locator(`tr:has-text("${subjectName}") button:has-text("删除")`);
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:116:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" classname="basic-flow.spec.js" time="14.854">
<failure message="basic-flow.spec.js:139:3 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" type="FAILURE">
<![CDATA[  [firefox] › basic-flow.spec.js:139:3 › 期末复习平台 - 基础功能流程 › 完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页 ───────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-card-extra button:has-text("刷新")')


      159 |
      160 |     // 5. 刷新首页数据
    > 161 |     await page.click('.ant-card-extra button:has-text("刷新")');
          |                ^
      162 |     await page.waitForTimeout(2000);
      163 |
      164 |     // 6. 验证新创建的学科出现在首页
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:161:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › API响应时间性能测试" classname="basic-flow.spec.js" time="9.018">
<failure message="basic-flow.spec.js:170:3 API响应时间性能测试" type="FAILURE">
<![CDATA[  [firefox] › basic-flow.spec.js:170:3 › 期末复习平台 - 基础功能流程 › API响应时间性能测试 ─────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.
    Call log:
      - waiting for locator('.ant-table-tbody') to be visible


      183 |     await page.goto('/admin');
      184 |     const listStartTime = Date.now();
    > 185 |     await page.waitForSelector('.ant-table-tbody', { timeout: 5000 });
          |                ^
      186 |     const listLoadTime = Date.now() - listStartTime;
      187 |
      188 |     // 验证列表加载时间小于5秒
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:185:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="performance.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="firefox" tests="5" failures="4" skipped="0" time="72.843" errors="0">
<testcase name="性能测试套件 › 页面加载性能测试" classname="performance.spec.js" time="4.056">
<system-out>
<![CDATA[⚡ 开始页面加载性能测试
🏠 首页加载时间: 1673ms
⚙️ 管理后台加载时间: 1236ms
✅ 页面加载性能测试通过
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › API响应性能测试" classname="performance.spec.js" time="18.436">
<failure message="performance.spec.js:34:3 API响应性能测试" type="FAILURE">
<![CDATA[  [firefox] › performance.spec.js:34:3 › 性能测试套件 › API响应性能测试 ────────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      55 |     await page.click('button:has-text("新建学科")');
      56 |     await page.fill('input[placeholder="请输入学科名称"]', '性能测试学科');
    > 57 |     await page.click('.ant-modal button:has-text("创建")');
         |                ^
      58 |     await page.waitForTimeout(100);
      59 |     const createResponseTime = Date.now() - createStartTime;
      60 |     
        at D:\ai\Term Review\tests\e2e\performance.spec.js:57:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-API响应性能测试-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-API响应性能测试-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-API响应性能测试-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔌 开始API响应性能测试
📋 学科列表API响应时间: 1896ms

[[ATTACHMENT|performance-性能测试套件-API响应性能测试-firefox\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-API响应性能测试-firefox\video.webm]]

[[ATTACHMENT|performance-性能测试套件-API响应性能测试-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › 大量数据性能测试" classname="performance.spec.js" time="15.247">
<failure message="performance.spec.js:78:3 大量数据性能测试" type="FAILURE">
<![CDATA[  [firefox] › performance.spec.js:78:3 › 性能测试套件 › 大量数据性能测试 ─────────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      94 |       await page.fill('input[placeholder="请输入学科名称"]', `批量测试学科${i}`);
      95 |       await page.fill('textarea[placeholder="请输入学科描述（可选）"]', `这是第${i}个批量测试学科的描述`);
    > 96 |       await page.click('.ant-modal button:has-text("创建")');
         |                  ^
      97 |       await page.waitForTimeout(500);
      98 |     }
      99 |
        at D:\ai\Term Review\tests\e2e\performance.spec.js:96:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-大量数据性能测试-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-大量数据性能测试-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-大量数据性能测试-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[📊 开始大量数据性能测试

[[ATTACHMENT|performance-性能测试套件-大量数据性能测试-firefox\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-大量数据性能测试-firefox\video.webm]]

[[ATTACHMENT|performance-性能测试套件-大量数据性能测试-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › 内存使用性能测试" classname="performance.spec.js" time="16.756">
<failure message="performance.spec.js:130:3 内存使用性能测试" type="FAILURE">
<![CDATA[  [firefox] › performance.spec.js:130:3 › 性能测试套件 › 内存使用性能测试 ────────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      152 |       await page.click('button:has-text("新建学科")');
      153 |       await page.fill('input[placeholder="请输入学科名称"]', `内存测试学科${i}`);
    > 154 |       await page.click('.ant-modal button:has-text("创建")');
          |                  ^
      155 |       await page.waitForTimeout(500);
      156 |     }
      157 |
        at D:\ai\Term Review\tests\e2e\performance.spec.js:154:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-内存使用性能测试-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-内存使用性能测试-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-内存使用性能测试-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🧠 开始内存使用性能测试
🔍 初始内存使用: 0MB

[[ATTACHMENT|performance-性能测试套件-内存使用性能测试-firefox\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-内存使用性能测试-firefox\video.webm]]

[[ATTACHMENT|performance-性能测试套件-内存使用性能测试-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › 网络请求性能测试" classname="performance.spec.js" time="18.348">
<failure message="performance.spec.js:185:3 网络请求性能测试" type="FAILURE">
<![CDATA[  [firefox] › performance.spec.js:185:3 › 性能测试套件 › 网络请求性能测试 ────────────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      221 |     await page.locator('input[placeholder="请输入学科名称"]').blur();
      222 |     await page.waitForTimeout(1000);
    > 223 |     await page.click('.ant-modal button:has-text("创建")');
          |                ^
      224 |     await page.waitForTimeout(2000);
      225 |
      226 |     // 分析网络请求性能
        at D:\ai\Term Review\tests\e2e\performance.spec.js:223:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-网络请求性能测试-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\performance-性能测试套件-网络请求性能测试-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\performance-性能测试套件-网络请求性能测试-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🌐 开始网络请求性能测试

[[ATTACHMENT|performance-性能测试套件-网络请求性能测试-firefox\test-failed-1.png]]

[[ATTACHMENT|performance-性能测试套件-网络请求性能测试-firefox\video.webm]]

[[ATTACHMENT|performance-性能测试套件-网络请求性能测试-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="simple-test.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="firefox" tests="3" failures="0" skipped="0" time="9.939" errors="0">
<testcase name="简单功能验证 › 验证前后端连接正常" classname="simple-test.spec.js" time="4.865">
<system-out>
<![CDATA[✅ 前后端连接验证通过
]]>
</system-out>
</testcase>
<testcase name="简单功能验证 › 验证管理后台访问" classname="simple-test.spec.js" time="2.811">
<system-out>
<![CDATA[✅ 管理后台访问验证通过
]]>
</system-out>
</testcase>
<testcase name="简单功能验证 › 验证API响应性能" classname="simple-test.spec.js" time="2.263">
<system-out>
<![CDATA[✅ API响应性能验证通过 - 响应时间: 1390ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="subject-management.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="firefox" tests="4" failures="3" skipped="0" time="55.235" errors="0">
<testcase name="学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除" classname="subject-management.spec.js" time="10.634">
<failure message="subject-management.spec.js:21:3 完整的学科管理流程 - 创建、编辑、删除" type="FAILURE">
<![CDATA[  [firefox] › subject-management.spec.js:21:3 › 学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除 ──────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('input[value="card"]')
    Expected: visible
    Received: hidden
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('input[value="card"]')
        9 × locator resolved to <input checked type="radio" value="card" class="ant-radio-button-input"/>
          - unexpected value "hidden"


      27 |     
      28 |     // 验证视图切换按钮
    > 29 |     await expect(page.locator('input[value="card"]')).toBeVisible();
         |                                                       ^
      30 |     await expect(page.locator('input[value="table"]')).toBeVisible();
      31 |     
      32 |     console.log('✅ 管理后台页面加载验证通过');
        at D:\ai\Term Review\tests\e2e\subject-management.spec.js:29:55

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🧪 开始学科管理完整流程测试

[[ATTACHMENT|subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\test-failed-1.png]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\video.webm]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="学科管理完整功能测试 › 表单验证和错误处理测试" classname="subject-management.spec.js" time="15.747">
<failure message="subject-management.spec.js:141:3 表单验证和错误处理测试" type="FAILURE">
<![CDATA[  [firefox] › subject-management.spec.js:141:3 › 学科管理完整功能测试 › 表单验证和错误处理测试 ──────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      149 |     
      150 |     // 尝试提交空表单
    > 151 |     await page.click('.ant-modal button:has-text("创建")');
          |                ^
      152 |     
      153 |     // 验证验证错误信息
      154 |     await expect(page.locator('.ant-form-item-explain-error:has-text("请输入学科名称")')).toBeVisible();
        at D:\ai\Term Review\tests\e2e\subject-management.spec.js:151:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[🛡️ 开始表单验证和错误处理测试
📝 测试必填字段验证

[[ATTACHMENT|subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\test-failed-1.png]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\video.webm]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="学科管理完整功能测试 › 响应式设计测试" classname="subject-management.spec.js" time="10.481">
<system-out>
<![CDATA[📱 开始响应式设计测试
✅ 响应式设计测试通过
]]>
</system-out>
</testcase>
<testcase name="学科管理完整功能测试 › 性能测试" classname="subject-management.spec.js" time="18.373">
<failure message="subject-management.spec.js:243:3 性能测试" type="FAILURE">
<![CDATA[  [firefox] › subject-management.spec.js:243:3 › 学科管理完整功能测试 › 性能测试 ─────────────────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-modal button:has-text("创建")')


      257 |     await page.click('button:has-text("新建学科")');
      258 |     await page.fill('input[placeholder="请输入学科名称"]', '性能测试学科');
    > 259 |     await page.click('.ant-modal button:has-text("创建")');
          |                ^
      260 |     
      261 |     // 等待API响应
      262 |     await page.waitForTimeout(100);
        at D:\ai\Term Review\tests\e2e\subject-management.spec.js:259:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-性能测试-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\subject-management-学科管理完整功能测试-性能测试-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\subject-management-学科管理完整功能测试-性能测试-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[⚡ 开始性能测试
📊 页面加载时间: 1439ms

[[ATTACHMENT|subject-management-学科管理完整功能测试-性能测试-firefox\test-failed-1.png]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-性能测试-firefox\video.webm]]

[[ATTACHMENT|subject-management-学科管理完整功能测试-性能测试-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="basic-flow.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="webkit" tests="7" failures="6" skipped="0" time="69.708" errors="0">
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表" classname="basic-flow.spec.js" time="7.857">
<failure message="basic-flow.spec.js:11:3 用户可以访问首页并查看学科列表" type="FAILURE">
<![CDATA[  [webkit] › basic-flow.spec.js:11:3 › 期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表 ───────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.ant-card-extra button:has-text("刷新")')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.ant-card-extra button:has-text("刷新")')


      29 |
      30 |     // 验证刷新按钮（在卡片的extra区域）
    > 31 |     await expect(page.locator('.ant-card-extra button:has-text("刷新")')).toBeVisible();
         |                                                                         ^
      32 |
      33 |     console.log('✅ 首页访问和基本结构验证通过');
      34 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:31:73

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以执行健康检查" classname="basic-flow.spec.js" time="4.016">
<system-out>
<![CDATA[✅ 健康检查功能验证通过
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问管理后台" classname="basic-flow.spec.js" time="7.59">
<failure message="basic-flow.spec.js:52:3 用户可以访问管理后台" type="FAILURE">
<![CDATA[  [webkit] › basic-flow.spec.js:52:3 › 期末复习平台 - 基础功能流程 › 用户可以访问管理后台 ────────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.ant-table')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.ant-table')


      65 |     await expect(page.locator('button:has-text("新建学科")')).toBeVisible();
      66 |     await expect(page.locator('button:has-text("初始化数据库")')).toBeVisible();
    > 67 |     await expect(page.locator('.ant-table')).toBeVisible();
         |                                              ^
      68 |
      69 |     console.log('✅ 管理后台访问验证通过');
      70 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:67:46

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以创建新学科" classname="basic-flow.spec.js" time="11.636">
<failure message="basic-flow.spec.js:72:3 用户可以创建新学科" type="FAILURE">
<![CDATA[  [webkit] › basic-flow.spec.js:72:3 › 期末复习平台 - 基础功能流程 › 用户可以创建新学科 ─────────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('.ant-table-tbody')
    Expected string: "测试学科_1754533720474"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('.ant-table-tbody')


       95 |
       96 |     // 验证学科出现在列表中
    >  97 |     await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);
          |                                                    ^
       98 |
       99 |     console.log('✅ 创建学科功能验证通过');
      100 |   });
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:97:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以删除学科" classname="basic-flow.spec.js" time="11.721">
<failure message="basic-flow.spec.js:102:3 用户可以删除学科" type="FAILURE">
<![CDATA[  [webkit] › basic-flow.spec.js:102:3 › 期末复习平台 - 基础功能流程 › 用户可以删除学科 ─────────────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('.ant-table-tbody')
    Expected string: "待删除学科_1754533718568"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('.ant-table-tbody')


      114 |
      115 |     // 验证学科已创建
    > 116 |     await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);
          |                                                    ^
      117 |
      118 |     // 找到删除按钮并点击
      119 |     const deleteButton = page.locator(`tr:has-text("${subjectName}") button:has-text("删除")`);
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:116:52

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" classname="basic-flow.spec.js" time="17.881">
<failure message="basic-flow.spec.js:139:3 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" type="FAILURE">
<![CDATA[  [webkit] › basic-flow.spec.js:139:3 › 期末复习平台 - 基础功能流程 › 完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页 ────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.ant-card-extra button:has-text("刷新")')


      159 |
      160 |     // 5. 刷新首页数据
    > 161 |     await page.click('.ant-card-extra button:has-text("刷新")');
          |                ^
      162 |     await page.waitForTimeout(2000);
      163 |
      164 |     // 6. 验证新创建的学科出现在首页
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:161:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › API响应时间性能测试" classname="basic-flow.spec.js" time="9.007">
<failure message="basic-flow.spec.js:170:3 API响应时间性能测试" type="FAILURE">
<![CDATA[  [webkit] › basic-flow.spec.js:170:3 › 期末复习平台 - 基础功能流程 › API响应时间性能测试 ──────────────────────────────

    TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.
    Call log:
      - waiting for locator('.ant-table-tbody') to be visible


      183 |     await page.goto('/admin');
      184 |     const listStartTime = Date.now();
    > 185 |     await page.waitForSelector('.ant-table-tbody', { timeout: 5000 });
          |                ^
      186 |     const listLoadTime = Date.now() - listStartTime;
      187 |
      188 |     // 验证列表加载时间小于5秒
        at D:\ai\Term Review\tests\e2e\basic-flow.spec.js:185:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\test-failed-1.png]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\video.webm]]

[[ATTACHMENT|basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="performance.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="webkit" tests="5" failures="0" skipped="4" time="52.829" errors="0">
<testcase name="性能测试套件 › 页面加载性能测试" classname="performance.spec.js" time="3.032">
<system-out>
<![CDATA[⚡ 开始页面加载性能测试
🏠 首页加载时间: 1261ms
⚙️ 管理后台加载时间: 1148ms
✅ 页面加载性能测试通过
]]>
</system-out>
</testcase>
<testcase name="性能测试套件 › API响应性能测试" classname="performance.spec.js" time="15.784">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 大量数据性能测试" classname="performance.spec.js" time="12.022">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 内存使用性能测试" classname="performance.spec.js" time="11.635">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 网络请求性能测试" classname="performance.spec.js" time="10.356">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="simple-test.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="webkit" tests="3" failures="0" skipped="1" time="8.104" errors="0">
<testcase name="简单功能验证 › 验证前后端连接正常" classname="simple-test.spec.js" time="4.275">
<system-out>
<![CDATA[✅ 前后端连接验证通过
]]>
</system-out>
</testcase>
<testcase name="简单功能验证 › 验证管理后台访问" classname="simple-test.spec.js" time="2.294">
<system-out>
<![CDATA[✅ 管理后台访问验证通过
]]>
</system-out>
</testcase>
<testcase name="简单功能验证 › 验证API响应性能" classname="simple-test.spec.js" time="1.535">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="subject-management.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="webkit" tests="4" failures="0" skipped="4" time="2.258" errors="0">
<testcase name="学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除" classname="subject-management.spec.js" time="2.258">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 表单验证和错误处理测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 响应式设计测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 性能测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="basic-flow.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Chrome" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以执行健康检查" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问管理后台" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以创建新学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以删除学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › API响应时间性能测试" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="performance.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Chrome" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="性能测试套件 › 页面加载性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › API响应性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 大量数据性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 内存使用性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 网络请求性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="simple-test.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Chrome" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="简单功能验证 › 验证前后端连接正常" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证管理后台访问" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证API响应性能" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="subject-management.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Chrome" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 表单验证和错误处理测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 响应式设计测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 性能测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="basic-flow.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Safari" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以执行健康检查" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问管理后台" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以创建新学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以删除学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › API响应时间性能测试" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="performance.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Safari" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="性能测试套件 › 页面加载性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › API响应性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 大量数据性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 内存使用性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 网络请求性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="simple-test.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Safari" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="简单功能验证 › 验证前后端连接正常" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证管理后台访问" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证API响应性能" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="subject-management.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Mobile Safari" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 表单验证和错误处理测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 响应式设计测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 性能测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="basic-flow.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Microsoft Edge" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以执行健康检查" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问管理后台" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以创建新学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以删除学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › API响应时间性能测试" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="performance.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Microsoft Edge" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="性能测试套件 › 页面加载性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › API响应性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 大量数据性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 内存使用性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 网络请求性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="simple-test.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Microsoft Edge" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="简单功能验证 › 验证前后端连接正常" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证管理后台访问" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证API响应性能" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="subject-management.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Microsoft Edge" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 表单验证和错误处理测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 响应式设计测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 性能测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="basic-flow.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Google Chrome" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问首页并查看学科列表" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以执行健康检查" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以访问管理后台" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以创建新学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 用户可以删除学科" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › 完整用户流程：首页 -&gt; 管理后台 -&gt; 创建学科 -&gt; 返回首页" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="期末复习平台 - 基础功能流程 › API响应时间性能测试" classname="basic-flow.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="performance.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Google Chrome" tests="5" failures="0" skipped="5" time="0" errors="0">
<testcase name="性能测试套件 › 页面加载性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › API响应性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 大量数据性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 内存使用性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="性能测试套件 › 网络请求性能测试" classname="performance.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="simple-test.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Google Chrome" tests="3" failures="0" skipped="3" time="0" errors="0">
<testcase name="简单功能验证 › 验证前后端连接正常" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证管理后台访问" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="简单功能验证 › 验证API响应性能" classname="simple-test.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="subject-management.spec.js" timestamp="2025-08-07T02:27:15.421Z" hostname="Google Chrome" tests="4" failures="0" skipped="4" time="0" errors="0">
<testcase name="学科管理完整功能测试 › 完整的学科管理流程 - 创建、编辑、删除" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 表单验证和错误处理测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 响应式设计测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="学科管理完整功能测试 › 性能测试" classname="subject-management.spec.js" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>