// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('期末复习平台 - 基础功能流程', () => {

  test.beforeEach(async ({ page }) => {
    // 设置更长的超时时间
    test.setTimeout(60000);
  });

  test('用户可以访问首页并查看学科列表', async ({ page }) => {
    // 访问首页
    await page.goto('/');

    // 验证页面标题
    await expect(page).toHaveTitle(/期末复习平台/);

    // 验证页面标题元素
    await expect(page.locator('.title')).toContainText('期末复习平台');

    // 验证页面基本结构
    await expect(page.locator('.ant-layout')).toBeVisible();
    await expect(page.locator('.ant-layout-header')).toBeVisible();
    await expect(page.locator('.ant-layout-content')).toBeVisible();

    // 验证操作按钮存在
    await expect(page.locator('button:has-text("健康检查")')).toBeVisible();
    await expect(page.locator('.header-content button:has-text("管理后台")')).toBeVisible();

    // 验证刷新按钮（在卡片的extra区域）
    await expect(page.locator('.ant-card-extra button:has-text("刷新")')).toBeVisible();

    console.log('✅ 首页访问和基本结构验证通过');
  });

  test('用户可以执行健康检查', async ({ page }) => {
    await page.goto('/');

    // 点击健康检查按钮
    await page.click('button:has-text("健康检查")');

    // 等待API响应并验证成功消息
    await page.waitForTimeout(2000);

    // 验证没有错误消息出现
    const errorMessages = page.locator('.ant-message-error');
    await expect(errorMessages).toHaveCount(0);

    console.log('✅ 健康检查功能验证通过');
  });

  test('用户可以访问管理后台', async ({ page }) => {
    await page.goto('/');

    // 点击管理后台按钮
    await page.click('.header-content button:has-text("管理后台")');

    // 验证跳转到管理后台页面
    await expect(page).toHaveURL('/admin');

    // 验证管理后台页面标题
    await expect(page.locator('.title')).toContainText('管理后台');

    // 验证管理后台基本功能
    await expect(page.locator('button:has-text("新建学科")')).toBeVisible();
    await expect(page.locator('button:has-text("初始化数据库")')).toBeVisible();
    await expect(page.locator('.ant-table')).toBeVisible();

    console.log('✅ 管理后台访问验证通过');
  });

  test('用户可以创建新学科', async ({ page }) => {
    await page.goto('/admin');

    // 点击新建学科按钮
    await page.click('button:has-text("新建学科")');

    // 验证模态框出现
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('.ant-modal-title')).toContainText('新建学科');

    // 填写学科信息
    const subjectName = `测试学科_${Date.now()}`;
    await page.fill('input[placeholder="请输入学科名称"]', subjectName);
    await page.fill('textarea[placeholder="请输入学科描述（可选）"]', '这是一个E2E测试创建的学科');

    // 提交表单
    await page.click('.ant-modal-footer button.ant-btn-primary');

    // 等待创建完成
    await page.waitForTimeout(2000);

    // 验证模态框关闭
    await expect(page.locator('.ant-modal')).not.toBeVisible();

    // 验证学科出现在列表中
    await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);

    console.log('✅ 创建学科功能验证通过');
  });

  test('用户可以删除学科', async ({ page }) => {
    await page.goto('/admin');

    // 先创建一个学科用于删除
    await page.click('button:has-text("新建学科")');
    await expect(page.locator('.ant-modal')).toBeVisible();

    const subjectName = `待删除学科_${Date.now()}`;
    await page.fill('input[placeholder="请输入学科名称"]', subjectName);
    await page.fill('textarea[placeholder="请输入学科描述（可选）"]', '这个学科将被删除');
    await page.click('.ant-modal-footer button.ant-btn-primary');
    await page.waitForTimeout(2000);

    // 验证学科已创建
    await expect(page.locator('.ant-table-tbody')).toContainText(subjectName);

    // 找到删除按钮并点击
    const deleteButton = page.locator(`tr:has-text("${subjectName}") button:has-text("删除")`);
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();

    // 确认删除
    await page.waitForTimeout(1000);
    const confirmButton = page.locator('.ant-popconfirm button.ant-btn-primary');
    if (await confirmButton.isVisible()) {
      await confirmButton.click();
    }

    // 等待删除完成
    await page.waitForTimeout(2000);

    // 验证学科已从列表中移除
    await expect(page.locator('.ant-table-tbody')).not.toContainText(subjectName);

    console.log('✅ 删除学科功能验证通过');
  });

  test('完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页', async ({ page }) => {
    // 1. 访问首页
    await page.goto('/');
    await expect(page.locator('.title')).toContainText('期末复习平台');

    // 2. 进入管理后台
    await page.click('.header-content button:has-text("管理后台")');
    await expect(page).toHaveURL('/admin');

    // 3. 创建学科
    await page.click('button:has-text("新建学科")');
    const subjectName = `完整流程测试_${Date.now()}`;
    await page.fill('input[placeholder="请输入学科名称"]', subjectName);
    await page.fill('textarea[placeholder="请输入学科描述（可选）"]', '完整流程测试学科');
    await page.click('.ant-modal-footer button.ant-btn-primary');
    await page.waitForTimeout(2000);

    // 4. 返回首页
    await page.click('button:has-text("返回首页")');
    await expect(page).toHaveURL('/');

    // 5. 刷新首页数据
    await page.click('.ant-card-extra button:has-text("刷新")');
    await page.waitForTimeout(2000);

    // 6. 验证新创建的学科出现在首页
    await expect(page.locator('.ant-card')).toContainText(subjectName);

    console.log('✅ 完整用户流程验证通过');
  });

  test('API响应时间性能测试', async ({ page }) => {
    await page.goto('/');

    // 测试健康检查API响应时间
    const startTime = Date.now();
    await page.click('button:has-text("健康检查")');
    await page.waitForTimeout(1000);
    const healthCheckTime = Date.now() - startTime;

    // 验证响应时间小于3秒
    expect(healthCheckTime).toBeLessThan(3000);

    // 测试学科列表加载时间
    await page.goto('/admin');
    const listStartTime = Date.now();
    await page.waitForSelector('.ant-table-tbody', { timeout: 5000 });
    const listLoadTime = Date.now() - listStartTime;

    // 验证列表加载时间小于5秒
    expect(listLoadTime).toBeLessThan(5000);

    console.log(`✅ 性能测试通过 - 健康检查: ${healthCheckTime}ms, 列表加载: ${listLoadTime}ms`);
  });
});
