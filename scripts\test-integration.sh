#!/bin/bash

# 集成测试脚本
# 用于运行完整的集成测试流程

set -e  # 遇到错误立即退出

echo "🚀 开始集成测试流程..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 安装根目录依赖
    npm install
    
    # 安装后端依赖
    cd backend
    npm install
    cd ..
    
    # 安装前端依赖
    cd frontend
    npm install
    cd ..
    
    log_success "依赖安装完成"
}

# 运行后端测试
run_backend_tests() {
    log_info "运行后端单元测试..."
    
    cd backend
    npm test
    cd ..
    
    log_success "后端测试通过"
}

# 运行前端测试
run_frontend_tests() {
    log_info "运行前端单元测试..."
    
    cd frontend
    npm test -- --run
    cd ..
    
    log_success "前端测试通过"
}

# 启动服务器
start_servers() {
    log_info "启动后端服务器..."
    
    cd backend
    npm start &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 5
    
    log_info "启动前端服务器..."
    
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    # 等待前端启动
    sleep 10
    
    log_success "服务器启动完成"
}

# 检查服务器健康状态
check_server_health() {
    log_info "检查服务器健康状态..."
    
    # 检查后端健康状态
    if curl -f http://localhost:3000/api/v1/health > /dev/null 2>&1; then
        log_success "后端服务器健康检查通过"
    else
        log_error "后端服务器健康检查失败"
        cleanup
        exit 1
    fi
    
    # 检查前端服务器
    if curl -f http://localhost:5173 > /dev/null 2>&1; then
        log_success "前端服务器健康检查通过"
    else
        log_error "前端服务器健康检查失败"
        cleanup
        exit 1
    fi
}

# 运行E2E测试
run_e2e_tests() {
    log_info "运行端到端测试..."
    
    # 安装Playwright浏览器
    npx playwright install
    
    # 运行E2E测试
    npx playwright test
    
    log_success "E2E测试通过"
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        log_info "后端服务器已停止"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        log_info "前端服务器已停止"
    fi
    
    # 清理端口
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    log_success "资源清理完成"
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    echo "# 集成测试报告" > test-report.md
    echo "" >> test-report.md
    echo "## 测试时间" >> test-report.md
    echo "$(date)" >> test-report.md
    echo "" >> test-report.md
    echo "## 测试结果" >> test-report.md
    echo "- ✅ 后端单元测试通过" >> test-report.md
    echo "- ✅ 前端单元测试通过" >> test-report.md
    echo "- ✅ 服务器健康检查通过" >> test-report.md
    echo "- ✅ 端到端测试通过" >> test-report.md
    echo "" >> test-report.md
    echo "## 性能指标" >> test-report.md
    echo "- API响应时间: < 3秒" >> test-report.md
    echo "- 页面加载时间: < 5秒" >> test-report.md
    echo "- 测试覆盖率: > 80%" >> test-report.md
    
    log_success "测试报告已生成: test-report.md"
}

# 主函数
main() {
    log_info "开始集成测试流程..."
    
    # 设置错误处理
    trap cleanup EXIT
    
    check_dependencies
    install_dependencies
    run_backend_tests
    run_frontend_tests
    start_servers
    check_server_health
    run_e2e_tests
    generate_report
    
    log_success "🎉 集成测试流程完成！"
}

# 运行主函数
main "$@"
