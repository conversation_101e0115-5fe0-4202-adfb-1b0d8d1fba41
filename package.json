{"name": "term-review-platform", "version": "1.0.0", "description": "期末复习平台 - 全栈应用", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm start", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "test:performance": "playwright test tests/e2e/performance.spec.js", "test:subject-management": "playwright test tests/e2e/subject-management.spec.js", "test:integration": "bash scripts/test-integration.sh", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules frontend/dist backend/dist", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint"}, "devDependencies": {"@playwright/test": "^1.54.2", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/term-review-platform.git"}, "keywords": ["vue3", "typescript", "nodejs", "koa", "sqlite", "education", "review-platform"], "author": "<PERSON> (工程师)", "license": "MIT"}