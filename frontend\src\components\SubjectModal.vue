<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    :ok-text="okText"
    cancel-text="取消"
    :confirm-loading="loading"
    :width="600"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      class="subject-form"
    >
      <a-form-item label="学科名称" name="name" class="form-item-required">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入学科名称"
          :maxlength="50"
          show-count
          @blur="handleNameBlur"
          @input="handleNameInput"
        />
        <div v-if="nameCheckResult" class="name-check-result">
          <a-alert
            v-if="!nameCheckResult.available"
            :message="`学科名称 '${nameCheckResult.name}' 已存在`"
            type="error"
            show-icon
            :closable="false"
            class="name-check-alert"
          />
          <a-alert
            v-else-if="nameCheckResult.available && formData.name"
            :message="`学科名称 '${nameCheckResult.name}' 可以使用`"
            type="success"
            show-icon
            :closable="false"
            class="name-check-alert"
          />
        </div>
      </a-form-item>
      
      <a-form-item label="学科描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入学科描述（可选）"
          :maxlength="500"
          :rows="4"
          show-count
        />
      </a-form-item>
      
      <div v-if="mode === 'edit' && subject" class="subject-info">
        <a-divider>学科信息</a-divider>
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="创建时间">
            {{ formatDate(subject.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDate(subject.updated_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="文件数量" v-if="subject.file_count !== undefined">
            {{ subject.file_count }} 个
          </a-descriptions-item>
          <a-descriptions-item label="总大小" v-if="subject.total_size !== undefined">
            {{ formatFileSize(subject.total_size) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { ApiService, type Subject, type CreateSubjectRequest, type UpdateSubjectRequest } from '@/services/api'

interface Props {
  open: boolean
  mode: 'create' | 'edit'
  subject?: Subject
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'success', subject: Subject): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create'
})

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const nameCheckLoading = ref(false)
const nameCheckResult = ref<{ name: string; available: boolean; conflictId?: number } | null>(null)

// 表单数据
const formData = reactive<CreateSubjectRequest & UpdateSubjectRequest>({
  name: '',
  description: ''
})

// 计算属性
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const modalTitle = computed(() => {
  return props.mode === 'create' ? '新建学科' : '编辑学科'
})

const okText = computed(() => {
  return props.mode === 'create' ? '创建' : '保存'
})

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入学科名称' },
    { min: 1, max: 50, message: '学科名称长度应在1-50个字符之间' },
    { 
      pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/, 
      message: '学科名称只能包含中英文、数字、空格、连字符和下划线' 
    },
    {
      validator: async (rule, value) => {
        if (!value) return Promise.resolve()
        
        // 如果名称检查结果存在且不可用，则验证失败
        if (nameCheckResult.value && !nameCheckResult.value.available) {
          return Promise.reject(new Error('学科名称已存在'))
        }
        
        return Promise.resolve()
      }
    }
  ],
  description: [
    { max: 500, message: '学科描述不能超过500个字符' }
  ]
}

// 监听弹窗打开状态
watch(() => props.open, (newVal) => {
  if (newVal) {
    initForm()
  } else {
    resetForm()
  }
})

// 初始化表单
const initForm = () => {
  if (props.mode === 'edit' && props.subject) {
    formData.name = props.subject.name
    formData.description = props.subject.description || ''
  } else {
    formData.name = ''
    formData.description = ''
  }
  nameCheckResult.value = null
}

// 重置表单
const resetForm = () => {
  formData.name = ''
  formData.description = ''
  nameCheckResult.value = null
  formRef.value?.resetFields()
}

// 处理名称输入
const handleNameInput = () => {
  // 清除之前的检查结果
  nameCheckResult.value = null
}

// 处理名称失焦
const handleNameBlur = async () => {
  if (!formData.name.trim()) {
    nameCheckResult.value = null
    return
  }
  
  await checkNameAvailability(formData.name.trim())
}

// 检查名称可用性
const checkNameAvailability = async (name: string) => {
  if (nameCheckLoading.value) return
  
  try {
    nameCheckLoading.value = true
    
    const excludeId = props.mode === 'edit' && props.subject ? props.subject.id : undefined
    const response = await ApiService.checkSubjectName(name, excludeId)
    
    if (response.success) {
      nameCheckResult.value = response.data
    }
  } catch (error) {
    console.error('检查名称可用性失败:', error)
    message.error('检查名称可用性失败')
  } finally {
    nameCheckLoading.value = false
  }
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    // 检查名称可用性
    if (!nameCheckResult.value) {
      await checkNameAvailability(formData.name.trim())
      await nextTick() // 等待检查结果更新
    }
    
    if (nameCheckResult.value && !nameCheckResult.value.available) {
      message.error('学科名称已存在，请修改后重试')
      return
    }
    
    loading.value = true
    
    let response
    if (props.mode === 'create') {
      response = await ApiService.createSubject({
        name: formData.name.trim(),
        description: formData.description?.trim() || undefined
      })
    } else if (props.subject) {
      response = await ApiService.updateSubject(props.subject.id, {
        name: formData.name.trim(),
        description: formData.description?.trim() || undefined
      })
    }
    
    if (response?.success) {
      message.success(props.mode === 'create' ? '学科创建成功' : '学科更新成功')
      emit('success', response.data)
      visible.value = false
    }
  } catch (error: any) {
    console.error('提交失败:', error)
    if (error.response?.data?.error) {
      message.error(error.response.data.error)
    } else {
      message.error(props.mode === 'create' ? '创建学科失败' : '更新学科失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.subject-form {
  padding: 8px 0;
}

.form-item-required :deep(.ant-form-item-label > label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

.name-check-result {
  margin-top: 8px;
}

.name-check-alert {
  font-size: 12px;
}

.subject-info {
  margin-top: 16px;
}

.subject-info :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #595959;
}

.subject-info :deep(.ant-descriptions-item-content) {
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subject-info :deep(.ant-descriptions) {
    column-count: 1;
  }
}
</style>
