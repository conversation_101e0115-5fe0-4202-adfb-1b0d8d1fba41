const Joi = require('joi');

// 学科数据验证规则
const subjectSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(50)
    .pattern(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/)
    .required()
    .messages({
      'string.empty': '学科名称不能为空',
      'string.min': '学科名称至少需要1个字符',
      'string.max': '学科名称不能超过50个字符',
      'string.pattern.base': '学科名称只能包含中英文、数字、空格、连字符和下划线',
      'any.required': '学科名称是必填项'
    }),
  description: Joi.string()
    .max(500)
    .allow('')
    .optional()
    .messages({
      'string.max': '学科描述不能超过500个字符'
    })
});

// 学科创建验证规则
const subjectCreateSchema = subjectSchema;

// 学科更新验证规则
const subjectUpdateSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(50)
    .pattern(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/)
    .optional()
    .messages({
      'string.empty': '学科名称不能为空',
      'string.min': '学科名称至少需要1个字符',
      'string.max': '学科名称不能超过50个字符',
      'string.pattern.base': '学科名称只能包含中英文、数字、空格、连字符和下划线'
    }),
  description: Joi.string()
    .max(500)
    .allow('')
    .optional()
    .messages({
      'string.max': '学科描述不能超过500个字符'
    })
}).min(1).messages({
  'object.min': '至少需要提供一个要更新的字段'
});

// 学科验证中间件（创建）
const validateSubject = async (ctx, next) => {
  try {
    const { error, value } = subjectCreateSchema.validate(ctx.request.body, {
      abortEarly: false, // 返回所有验证错误
      stripUnknown: true // 移除未知字段
    });

    if (error) {
      const errorMessages = error.details.map(detail => detail.message);

      ctx.status = 400;
      ctx.body = {
        success: false,
        code: 400,
        message: '请求参数验证失败',
        error: errorMessages.join('; '),
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context.value
        })),
        timestamp: new Date().toISOString()
      };
      return;
    }

    // 将验证后的数据替换原始数据
    ctx.request.body = value;

    await next();
  } catch (err) {
    console.error('参数验证中间件错误:', err);

    ctx.status = 500;
    ctx.body = {
      success: false,
      code: 500,
      message: '参数验证失败',
      error: '服务器内部错误',
      timestamp: new Date().toISOString()
    };
  }
};

// 学科更新验证中间件
const validateSubjectUpdate = async (ctx, next) => {
  try {
    const { error, value } = subjectUpdateSchema.validate(ctx.request.body, {
      abortEarly: false, // 返回所有验证错误
      stripUnknown: true // 移除未知字段
    });

    if (error) {
      const errorMessages = error.details.map(detail => detail.message);

      ctx.status = 400;
      ctx.body = {
        success: false,
        code: 400,
        message: '请求参数验证失败',
        error: errorMessages.join('; '),
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context.value
        })),
        timestamp: new Date().toISOString()
      };
      return;
    }

    // 将验证后的数据替换原始数据
    ctx.request.body = value;

    await next();
  } catch (err) {
    console.error('参数验证中间件错误:', err);

    ctx.status = 500;
    ctx.body = {
      success: false,
      code: 500,
      message: '参数验证失败',
      error: '服务器内部错误',
      timestamp: new Date().toISOString()
    };
  }
};

// ID参数验证中间件
const validateId = (paramName = 'id') => {
  return async (ctx, next) => {
    try {
      const id = parseInt(ctx.params[paramName]);

      if (isNaN(id) || id <= 0) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: `${paramName}必须是正整数`,
          timestamp: new Date().toISOString()
        };
        return;
      }

      // 将验证后的ID添加到上下文
      ctx.validatedId = id;

      await next();
    } catch (err) {
      console.error('ID验证中间件错误:', err);

      ctx.status = 500;
      ctx.body = {
        success: false,
        code: 500,
        message: 'ID参数验证失败',
        error: '服务器内部错误',
        timestamp: new Date().toISOString()
      };
    }
  };
};

// 通用验证函数
const validate = (schema) => {
  return async (ctx, next) => {
    try {
      const { error, value } = schema.validate(ctx.request.body, {
        abortEarly: false,
        stripUnknown: true
      });

      if (error) {
        const errorMessages = error.details.map(detail => detail.message);

        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数验证失败',
          error: errorMessages.join('; '),
          details: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
            value: detail.context.value
          })),
          timestamp: new Date().toISOString()
        };
        return;
      }

      ctx.request.body = value;
      await next();
    } catch (err) {
      console.error('通用验证中间件错误:', err);

      ctx.status = 500;
      ctx.body = {
        success: false,
        code: 500,
        message: '参数验证失败',
        error: '服务器内部错误',
        timestamp: new Date().toISOString()
      };
    }
  };
};

module.exports = {
  validateSubject,
  validateSubjectUpdate,
  validateId,
  validate,
  subjectSchema,
  subjectCreateSchema,
  subjectUpdateSchema
};
