// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('简单功能验证', () => {
  
  test('验证前后端连接正常', async ({ page }) => {
    // 访问首页
    await page.goto('/');
    
    // 验证页面标题
    await expect(page).toHaveTitle(/期末复习平台/);
    
    // 验证页面基本结构
    await expect(page.locator('.title')).toContainText('期末复习平台');
    
    // 验证健康检查按钮存在并可点击
    const healthButton = page.locator('button:has-text("健康检查")');
    await expect(healthButton).toBeVisible();
    await healthButton.click();
    
    // 等待响应
    await page.waitForTimeout(2000);
    
    console.log('✅ 前后端连接验证通过');
  });

  test('验证管理后台访问', async ({ page }) => {
    await page.goto('/');
    
    // 点击头部的管理后台按钮
    await page.click('.header-content button:has-text("管理后台")');
    
    // 验证跳转到管理后台
    await expect(page).toHaveURL('/admin');
    
    // 验证管理后台页面加载
    await expect(page.locator('.title')).toContainText('管理后台');
    
    console.log('✅ 管理后台访问验证通过');
  });

  test('验证API响应性能', async ({ page }) => {
    await page.goto('/');
    
    // 测试健康检查API响应时间
    const startTime = Date.now();
    await page.click('button:has-text("健康检查")');
    await page.waitForTimeout(1000);
    const responseTime = Date.now() - startTime;
    
    // 验证响应时间小于3秒
    expect(responseTime).toBeLessThan(3000);
    
    console.log(`✅ API响应性能验证通过 - 响应时间: ${responseTime}ms`);
  });
});
