<template>
  <a-card 
    class="subject-card" 
    :class="{ 'subject-card--selected': selected }"
    hoverable
    @click="$emit('click', subject)"
  >
    <template #title>
      <div class="subject-card__title">
        <span class="subject-name">{{ subject.name }}</span>
        <a-tag v-if="subject.file_count !== undefined" color="blue" class="file-count-tag">
          {{ subject.file_count }} 个文件
        </a-tag>
      </div>
    </template>
    
    <template #extra>
      <a-dropdown :trigger="['click']" placement="bottomRight">
        <a-button type="text" size="small" @click.stop>
          <template #icon>
            <MoreOutlined />
          </template>
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item key="edit" @click="handleEdit">
              <template #icon>
                <EditOutlined />
              </template>
              编辑
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="delete" danger @click="handleDelete">
              <template #icon>
                <DeleteOutlined />
              </template>
              删除
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>

    <div class="subject-card__content">
      <p class="subject-description" v-if="subject.description">
        {{ subject.description }}
      </p>
      <p class="subject-description empty" v-else>
        暂无描述
      </p>
      
      <div class="subject-stats" v-if="showStats">
        <div class="stat-item">
          <span class="stat-label">文件数量:</span>
          <span class="stat-value">{{ subject.file_count || 0 }}</span>
        </div>
        <div class="stat-item" v-if="subject.total_size !== undefined">
          <span class="stat-label">总大小:</span>
          <span class="stat-value">{{ formatFileSize(subject.total_size) }}</span>
        </div>
      </div>
      
      <div class="subject-meta">
        <a-space size="small">
          <span class="meta-item">
            <CalendarOutlined />
            创建于 {{ formatDate(subject.created_at) }}
          </span>
          <span class="meta-item" v-if="subject.updated_at !== subject.created_at">
            <EditOutlined />
            更新于 {{ formatDate(subject.updated_at) }}
          </span>
        </a-space>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  MoreOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  CalendarOutlined 
} from '@ant-design/icons-vue'
import type { Subject } from '@/services/api'

interface Props {
  subject: Subject
  selected?: boolean
  showStats?: boolean
}

interface Emits {
  (e: 'click', subject: Subject): void
  (e: 'edit', subject: Subject): void
  (e: 'delete', subject: Subject): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  showStats: true
})

const emit = defineEmits<Emits>()

// 处理编辑操作
const handleEdit = (e: Event) => {
  e.stopPropagation()
  emit('edit', props.subject)
}

// 处理删除操作
const handleDelete = (e: Event) => {
  e.stopPropagation()
  emit('delete', props.subject)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return '今天'
  } else if (diffDays === 2) {
    return '昨天'
  } else if (diffDays <= 7) {
    return `${diffDays - 1} 天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}
</script>

<style scoped>
.subject-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 16px;
}

.subject-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.subject-card--selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.subject-card__title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.subject-name {
  font-weight: 600;
  font-size: 16px;
  color: #262626;
  flex: 1;
  margin-right: 8px;
}

.file-count-tag {
  font-size: 12px;
  border-radius: 12px;
}

.subject-card__content {
  padding: 0;
}

.subject-description {
  color: #595959;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.subject-description.empty {
  color: #bfbfbf;
  font-style: italic;
}

.subject-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #fafafa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.stat-value {
  font-size: 12px;
  font-weight: 500;
  color: #262626;
}

.subject-meta {
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

.meta-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subject-card__title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .subject-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .subject-meta .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .subject-card {
    margin-bottom: 12px;
  }
  
  .subject-name {
    font-size: 14px;
  }
  
  .subject-description {
    font-size: 13px;
  }
}
</style>
