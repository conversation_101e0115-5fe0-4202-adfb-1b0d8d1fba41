const Joi = require('joi');

/**
 * 学科数据验证工具类
 * 提供完整的学科数据验证功能，支持创建、更新、名称检查等场景
 */
class SubjectValidator {
  constructor() {
    // 学科名称验证规则
    this.nameSchema = Joi.string()
      .min(1)
      .max(50)
      .pattern(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/)
      .required()
      .messages({
        'string.empty': '学科名称不能为空',
        'string.min': '学科名称至少需要1个字符',
        'string.max': '学科名称不能超过50个字符',
        'string.pattern.base': '学科名称只能包含中英文、数字、空格、连字符和下划线',
        'any.required': '学科名称是必填项'
      });

    // 学科描述验证规则
    this.descriptionSchema = Joi.string()
      .max(500)
      .allow('')
      .optional()
      .messages({
        'string.max': '学科描述不能超过500个字符'
      });

    // 创建学科验证规则
    this.createSchema = Joi.object({
      name: this.nameSchema,
      description: this.descriptionSchema
    });

    // 更新学科验证规则
    this.updateSchema = Joi.object({
      name: this.nameSchema.optional(),
      description: this.descriptionSchema
    }).min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    });

    // 名称检查验证规则
    this.checkNameSchema = Joi.object({
      name: this.nameSchema,
      excludeId: Joi.alternatives().try(
        Joi.number().integer().positive(),
        Joi.valid(null)
      ).optional()
    });

    // ID验证规则
    this.idSchema = Joi.number().integer().positive().required().messages({
      'number.base': 'ID必须是数字',
      'number.integer': 'ID必须是整数',
      'number.positive': 'ID必须是正数',
      'any.required': 'ID是必填项'
    });
  }

  /**
   * 验证创建学科的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证结果 {isValid, data, errors}
   */
  validateCreate(data) {
    try {
      const { error, value } = this.createSchema.validate(data, {
        abortEarly: false,
        stripUnknown: true
      });

      if (error) {
        return {
          isValid: false,
          data: null,
          errors: this._formatErrors(error)
        };
      }

      return {
        isValid: true,
        data: value,
        errors: null
      };
    } catch (err) {
      return {
        isValid: false,
        data: null,
        errors: ['数据验证过程中发生错误']
      };
    }
  }

  /**
   * 验证更新学科的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证结果 {isValid, data, errors}
   */
  validateUpdate(data) {
    try {
      const { error, value } = this.updateSchema.validate(data, {
        abortEarly: false,
        stripUnknown: true
      });

      if (error) {
        return {
          isValid: false,
          data: null,
          errors: this._formatErrors(error)
        };
      }

      return {
        isValid: true,
        data: value,
        errors: null
      };
    } catch (err) {
      return {
        isValid: false,
        data: null,
        errors: ['数据验证过程中发生错误']
      };
    }
  }

  /**
   * 验证名称检查的数据
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证结果 {isValid, data, errors}
   */
  validateCheckName(data) {
    try {
      const { error, value } = this.checkNameSchema.validate(data, {
        abortEarly: false,
        stripUnknown: true
      });

      if (error) {
        return {
          isValid: false,
          data: null,
          errors: this._formatErrors(error)
        };
      }

      return {
        isValid: true,
        data: value,
        errors: null
      };
    } catch (err) {
      return {
        isValid: false,
        data: null,
        errors: ['数据验证过程中发生错误']
      };
    }
  }

  /**
   * 验证学科ID
   * @param {*} id - 要验证的ID
   * @returns {Object} 验证结果 {isValid, data, errors}
   */
  validateId(id) {
    try {
      const { error, value } = this.idSchema.validate(id);

      if (error) {
        return {
          isValid: false,
          data: null,
          errors: this._formatErrors(error)
        };
      }

      return {
        isValid: true,
        data: value,
        errors: null
      };
    } catch (err) {
      return {
        isValid: false,
        data: null,
        errors: ['ID验证过程中发生错误']
      };
    }
  }

  /**
   * 验证学科名称格式
   * @param {string} name - 要验证的名称
   * @returns {Object} 验证结果 {isValid, data, errors}
   */
  validateName(name) {
    try {
      const { error, value } = this.nameSchema.validate(name);

      if (error) {
        return {
          isValid: false,
          data: null,
          errors: this._formatErrors(error)
        };
      }

      return {
        isValid: true,
        data: value,
        errors: null
      };
    } catch (err) {
      return {
        isValid: false,
        data: null,
        errors: ['名称验证过程中发生错误']
      };
    }
  }

  /**
   * 格式化验证错误信息
   * @private
   * @param {Object} error - Joi验证错误对象
   * @returns {Array} 格式化后的错误信息数组
   */
  _formatErrors(error) {
    return error.details.map(detail => detail.message);
  }

  /**
   * 获取详细的验证错误信息
   * @private
   * @param {Object} error - Joi验证错误对象
   * @returns {Array} 详细的错误信息数组
   */
  _getDetailedErrors(error) {
    return error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context.value
    }));
  }

  /**
   * 验证并返回详细错误信息
   * @param {string} type - 验证类型 ('create', 'update', 'checkName', 'id', 'name')
   * @param {*} data - 要验证的数据
   * @returns {Object} 验证结果，包含详细错误信息
   */
  validateWithDetails(type, data) {
    let result;

    switch (type) {
      case 'create':
        result = this.validateCreate(data);
        break;
      case 'update':
        result = this.validateUpdate(data);
        break;
      case 'checkName':
        result = this.validateCheckName(data);
        break;
      case 'id':
        result = this.validateId(data);
        break;
      case 'name':
        result = this.validateName(data);
        break;
      default:
        return {
          isValid: false,
          data: null,
          errors: ['不支持的验证类型'],
          details: []
        };
    }

    // 如果有错误，获取详细信息
    if (!result.isValid && result.errors) {
      try {
        const schema = this._getSchemaByType(type);
        const { error } = schema.validate(data, { abortEarly: false });
        result.details = error ? this._getDetailedErrors(error) : [];
      } catch (err) {
        result.details = [];
      }
    }

    return result;
  }

  /**
   * 根据验证类型获取对应的schema
   * @private
   * @param {string} type - 验证类型
   * @returns {Object} 对应的Joi schema
   */
  _getSchemaByType(type) {
    switch (type) {
      case 'create':
        return this.createSchema;
      case 'update':
        return this.updateSchema;
      case 'checkName':
        return this.checkNameSchema;
      case 'id':
        return this.idSchema;
      case 'name':
        return this.nameSchema;
      default:
        return Joi.any();
    }
  }
}

// 创建单例实例
const subjectValidator = new SubjectValidator();

module.exports = {
  SubjectValidator,
  subjectValidator
};
