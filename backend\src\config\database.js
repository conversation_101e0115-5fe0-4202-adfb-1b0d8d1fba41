// 简化的内存数据库实现用于测试
class MockDatabase {
  constructor() {
    this.subjects = [];
    this.nextId = 1;
  }

  // 模拟 prepare 方法
  prepare(sql) {
    return {
      all: () => {
        if (sql.includes('SELECT') && sql.includes('subjects')) {
          return this.subjects.map(s => ({
            ...s,
            file_count: s.file_count || 0,
            total_size: s.total_size || 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }));
        }
        return [];
      },
      get: (id) => {
        if (sql.includes('SELECT') && sql.includes('WHERE id = ?')) {
          const subject = this.subjects.find(s => s.id === id);
          return subject ? {
            ...subject,
            file_count: subject.file_count || 0,
            total_size: subject.total_size || 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } : null;
        }
        if (sql.includes('SELECT') && sql.includes('WHERE name = ?')) {
          const subject = this.subjects.find(s => s.name === id);
          return subject ? {
            ...subject,
            file_count: subject.file_count || 0,
            total_size: subject.total_size || 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } : null;
        }
        if (sql.includes('SELECT 1')) {
          return { '1': 1 };
        }
        return null;
      },
      run: (...params) => {
        if (sql.includes('INSERT INTO subjects')) {
          // 支持不同的INSERT格式
          let name, description, file_count = 0, total_size = 0;

          if (params.length === 2) {
            // 旧格式: name, description
            [name, description] = params;
          } else if (params.length === 4) {
            // 新格式: name, description, file_count, total_size
            [name, description, file_count, total_size] = params;
          }

          const existing = this.subjects.find(s => s.name === name);
          if (existing) {
            throw new Error('学科名称已存在');
          }
          const newSubject = {
            id: this.nextId++,
            name,
            description: description || null,
            file_count: file_count || 0,
            total_size: total_size || 0
          };
          this.subjects.push(newSubject);
          return { lastInsertRowid: newSubject.id, changes: 1 };
        }
        if (sql.includes('DELETE FROM subjects')) {
          const [id] = params;
          const index = this.subjects.findIndex(s => s.id === id);
          if (index === -1) {
            return { changes: 0 }; // 不抛出错误，返回changes: 0
          }
          this.subjects.splice(index, 1);
          return { changes: 1 };
        }
        if (sql.includes('UPDATE subjects')) {
          // 处理UPDATE操作
          const [name, description, id] = params;
          const index = this.subjects.findIndex(s => s.id === id);
          if (index === -1) {
            return { changes: 0 };
          }

          // 更新学科信息
          if (name !== null && name !== undefined) {
            this.subjects[index].name = name;
          }
          if (description !== null && description !== undefined) {
            this.subjects[index].description = description;
          }

          return { changes: 1 };
        }
        if (sql.includes('DELETE FROM file_nodes')) {
          // 模拟删除文件节点，返回删除的数量
          return { changes: 0 }; // 假设没有文件节点
        }
        return { changes: 0 };
      }
    };
  }

  // 模拟 exec 方法
  exec(sql) {
    // 处理清理数据的SQL
    if (sql.includes('DELETE FROM subjects')) {
      this.subjects = [];
      this.nextId = 1;
      return true;
    }
    if (sql.includes('DELETE FROM file_nodes') || sql.includes('DELETE FROM operation_logs') || sql.includes('DELETE FROM sqlite_sequence')) {
      // 忽略其他表的清理
      return true;
    }
    // 忽略表创建等SQL
    return true;
  }

  // 模拟 pragma 方法
  pragma(setting) {
    return true;
  }

  // 模拟 transaction 方法
  transaction(fn) {
    return () => {
      try {
        return fn();
      } catch (error) {
        throw error;
      }
    };
  }

  // 模拟 close 方法
  close() {
    return true;
  }
}

class DatabaseManager {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  // 初始化数据库连接
  initialize() {
    try {
      const env = process.env.NODE_ENV || 'development';

      // 测试环境使用Mock数据库
      if (env === 'test') {
        this.db = new MockDatabase();
      } else {
        // 开发和生产环境暂时使用Mock数据库
        // TODO: 在Sprint-2后期集成真实SQLite数据库
        console.log('使用Mock数据库进行开发和测试');
        this.db = new MockDatabase();
      }

      this.isInitialized = true;
      console.log(`数据库连接成功: ${env} 环境`);

      return this.db;
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }

  // 获取数据库实例
  getDatabase() {
    if (!this.isInitialized) {
      this.initialize();
    }
    return this.db;
  }

  // 检查数据库连接状态
  checkConnection() {
    try {
      if (!this.db) {
        return false;
      }
      // 执行简单查询测试连接
      this.db.prepare('SELECT 1').get();
      return true;
    } catch (error) {
      console.error('数据库连接检查失败:', error);
      return false;
    }
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close();
      this.isInitialized = false;
      console.log('数据库连接已关闭');
    }
  }

  // 创建数据库表结构
  createTables() {
    const db = this.getDatabase();

    try {
      // 检查是否为Mock数据库
      if (db instanceof MockDatabase) {
        console.log('数据库表结构创建成功 (Mock)');
        return true;
      }

      // 创建学科表 (subjects)
      db.exec(`
        CREATE TABLE IF NOT EXISTS subjects (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          file_count INTEGER DEFAULT 0,
          total_size INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 创建学科名称唯一索引
      db.exec(`
        CREATE UNIQUE INDEX IF NOT EXISTS idx_subjects_name
        ON subjects(name)
      `);

      // 创建文件节点表 (file_nodes) - 用于统计
      db.exec(`
        CREATE TABLE IF NOT EXISTS file_nodes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          subject_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          type TEXT NOT NULL CHECK(type IN ('file', 'folder')),
          file_size INTEGER DEFAULT 0,
          parent_id INTEGER,
          path TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
          FOREIGN KEY (parent_id) REFERENCES file_nodes(id) ON DELETE CASCADE
        )
      `);

      // 创建文件节点索引
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_id
        ON file_nodes(subject_id)
      `);

      // 创建操作日志表 (operation_logs)
      db.exec(`
        CREATE TABLE IF NOT EXISTS operation_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          operation_type TEXT NOT NULL,
          table_name TEXT NOT NULL,
          record_id INTEGER,
          old_data TEXT,
          new_data TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      console.log('数据库表结构创建成功 (SQLite)');
      return true;
    } catch (error) {
      console.error('创建数据库表结构失败:', error);
      throw error;
    }
  }

  // 插入示例数据
  insertSampleData() {
    const db = this.getDatabase();

    try {
      // Mock数据库预填充一些示例数据
      if (db instanceof MockDatabase) {
        if (db.subjects.length === 0) {
          db.subjects = [
            { id: 1, name: '数学', description: '高等数学、线性代数、概率论等数学相关内容', file_count: 0, total_size: 0 },
            { id: 2, name: '计算机科学', description: '数据结构、算法、编程语言等计算机科学内容', file_count: 0, total_size: 0 },
            { id: 3, name: '物理', description: '力学、电磁学、量子物理等物理学内容', file_count: 0, total_size: 0 }
          ];
          db.nextId = 4;
          console.log('示例数据插入成功 (Mock)');
          return true;
        }
        console.log('数据库已有数据，跳过示例数据插入');
        return false;
      }

      // 真实数据库插入示例数据
      const existingCount = db.prepare('SELECT COUNT(*) as count FROM subjects').get().count;
      if (existingCount === 0) {
        const insertStmt = db.prepare(`
          INSERT INTO subjects (name, description, file_count, total_size)
          VALUES (?, ?, ?, ?)
        `);

        const sampleData = [
          ['数学', '高等数学、线性代数、概率论等数学相关内容', 0, 0],
          ['计算机科学', '数据结构、算法、编程语言等计算机科学内容', 0, 0],
          ['物理', '力学、电磁学、量子物理等物理学内容', 0, 0]
        ];

        const transaction = db.transaction(() => {
          for (const data of sampleData) {
            insertStmt.run(...data);
          }
        });

        transaction();
        console.log('示例数据插入成功 (SQLite)');
        return true;
      }

      console.log('数据库已有数据，跳过示例数据插入');
      return false;
    } catch (error) {
      console.error('插入示例数据失败:', error);
      throw error;
    }
  }
}

// 创建全局数据库管理器实例
const dbManager = new DatabaseManager();

module.exports = {
  DatabaseManager,
  dbManager,
  getDatabase: () => dbManager.getDatabase(),
  checkConnection: () => dbManager.checkConnection(),
  createTables: () => dbManager.createTables(),
  insertSampleData: () => dbManager.insertSampleData()
};
