// global-teardown.js
async function globalTeardown() {
  console.log('🧹 Starting global teardown for E2E tests...');
  
  // 清理测试数据
  try {
    const { chromium } = require('@playwright/test');
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    // 清理数据库中的测试数据
    console.log('🗄️ Cleaning up test data...');
    
    // 这里可以添加清理逻辑，比如删除测试创建的学科
    // 目前使用Mock数据库，重启后会自动清理
    
    await browser.close();
    console.log('✅ Global teardown completed successfully');
    
  } catch (error) {
    console.error('⚠️ Global teardown encountered issues:', error);
    // 不抛出错误，避免影响测试结果
  }
}

module.exports = globalTeardown;
