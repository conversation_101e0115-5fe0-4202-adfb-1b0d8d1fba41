
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for config</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> config</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.97% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>80/138</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.71% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>65/87</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">78.57% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>22/28</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.72% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>73/131</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="database.js"><a href="database.js.html">database.js</a></td>
	<td data-value="57.97" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 57%"></div><div class="cover-empty" style="width: 43%"></div></div>
	</td>
	<td data-value="57.97" class="pct medium">57.97%</td>
	<td data-value="138" class="abs medium">80/138</td>
	<td data-value="74.71" class="pct medium">74.71%</td>
	<td data-value="87" class="abs medium">65/87</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="28" class="abs medium">22/28</td>
	<td data-value="55.72" class="pct medium">55.72%</td>
	<td data-value="131" class="abs medium">73/131</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-07T02:20:42.678Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    