
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50.8% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>349/687</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.15% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>132/286</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.5% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>55/88</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50.37% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>340/675</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="config"><a href="config/index.html">config</a></td>
	<td data-value="57.97" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 57%"></div><div class="cover-empty" style="width: 43%"></div></div>
	</td>
	<td data-value="57.97" class="pct medium">57.97%</td>
	<td data-value="138" class="abs medium">80/138</td>
	<td data-value="74.71" class="pct medium">74.71%</td>
	<td data-value="87" class="abs medium">65/87</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="28" class="abs medium">22/28</td>
	<td data-value="55.72" class="pct medium">55.72%</td>
	<td data-value="131" class="abs medium">73/131</td>
	</tr>

<tr>
	<td class="file medium" data-value="controllers"><a href="controllers/index.html">controllers</a></td>
	<td data-value="63.44" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 63%"></div><div class="cover-empty" style="width: 37%"></div></div>
	</td>
	<td data-value="63.44" class="pct medium">63.44%</td>
	<td data-value="93" class="abs medium">59/93</td>
	<td data-value="41.86" class="pct low">41.86%</td>
	<td data-value="43" class="abs low">18/43</td>
	<td data-value="83.33" class="pct medium">83.33%</td>
	<td data-value="6" class="abs medium">5/6</td>
	<td data-value="63.44" class="pct medium">63.44%</td>
	<td data-value="93" class="abs medium">59/93</td>
	</tr>

<tr>
	<td class="file low" data-value="middleware"><a href="middleware/index.html">middleware</a></td>
	<td data-value="48.78" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 48%"></div><div class="cover-empty" style="width: 52%"></div></div>
	</td>
	<td data-value="48.78" class="pct low">48.78%</td>
	<td data-value="123" class="abs low">60/123</td>
	<td data-value="22.64" class="pct low">22.64%</td>
	<td data-value="53" class="abs low">12/53</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="14" class="abs medium">8/14</td>
	<td data-value="48.33" class="pct low">48.33%</td>
	<td data-value="120" class="abs low">58/120</td>
	</tr>

<tr>
	<td class="file low" data-value="models"><a href="models/index.html">models</a></td>
	<td data-value="46.22" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 46%"></div><div class="cover-empty" style="width: 54%"></div></div>
	</td>
	<td data-value="46.22" class="pct low">46.22%</td>
	<td data-value="106" class="abs low">49/106</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="26" class="abs medium">13/26</td>
	<td data-value="64.28" class="pct medium">64.28%</td>
	<td data-value="14" class="abs medium">9/14</td>
	<td data-value="46.22" class="pct low">46.22%</td>
	<td data-value="106" class="abs low">49/106</td>
	</tr>

<tr>
	<td class="file low" data-value="routes"><a href="routes/index.html">routes</a></td>
	<td data-value="45.58" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 45%"></div><div class="cover-empty" style="width: 55%"></div></div>
	</td>
	<td data-value="45.58" class="pct low">45.58%</td>
	<td data-value="68" class="abs low">31/68</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="10" class="abs low">2/10</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="45.58" class="pct low">45.58%</td>
	<td data-value="68" class="abs low">31/68</td>
	</tr>

<tr>
	<td class="file medium" data-value="services"><a href="services/index.html">services</a></td>
	<td data-value="54.44" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.44" class="pct medium">54.44%</td>
	<td data-value="90" class="abs medium">49/90</td>
	<td data-value="48.71" class="pct low">48.71%</td>
	<td data-value="39" class="abs low">19/39</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="10" class="abs medium">6/10</td>
	<td data-value="54.44" class="pct medium">54.44%</td>
	<td data-value="90" class="abs medium">49/90</td>
	</tr>

<tr>
	<td class="file low" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="30.43" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 30%"></div><div class="cover-empty" style="width: 70%"></div></div>
	</td>
	<td data-value="30.43" class="pct low">30.43%</td>
	<td data-value="69" class="abs low">21/69</td>
	<td data-value="10.71" class="pct low">10.71%</td>
	<td data-value="28" class="abs low">3/28</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="12" class="abs low">4/12</td>
	<td data-value="31.34" class="pct low">31.34%</td>
	<td data-value="67" class="abs low">21/67</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-07T02:20:42.618Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    