{"config": {"configFile": "D:\\ai\\Term Review\\playwright.config.js", "rootDir": "D:/ai/Term Review/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "D:\\ai\\Term Review\\tests\\e2e\\global-setup.js", "globalTeardown": "D:\\ai\\Term Review\\tests\\e2e\\global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/ai/Term Review/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "D:/ai/Term Review/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ai/Term Review/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "D:/ai/Term Review/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ai/Term Review/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "D:/ai/Term Review/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ai/Term Review/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/ai/Term Review/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ai/Term Review/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/ai/Term Review/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ai/Term Review/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "D:/ai/Term Review/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ai/Term Review/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "D:/ai/Term Review/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 6, "webServer": null}, "suites": [{"title": "basic-flow.spec.js", "file": "basic-flow.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "期末复习平台 - 基础功能流程", "file": "basic-flow.spec.js", "line": 4, "column": 6, "specs": [{"title": "用户可以访问首页并查看学科列表", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 6583, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:31:73", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[90m// 验证刷新按钮（在卡片的extra区域）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 首页访问和基本结构验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 34 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[90m// 验证刷新按钮（在卡片的extra区域）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 首页访问和基本结构验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 34 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:31:73\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:17.833Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-c8884b869755e99517f6", "file": "basic-flow.spec.js", "line": 11, "column": 3}, {"title": "用户可以执行健康检查", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 3462, "errors": [], "stdout": [{"text": "✅ 健康检查功能验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:17.772Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a6c0d510a7f2fa3549e7-90e2a35ecbfa029494ca", "file": "basic-flow.spec.js", "line": 36, "column": 3}, {"title": "用户可以访问管理后台", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 6659, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:67:46", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}, "snippet": "\u001b[0m \u001b[90m 65 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 68 |\u001b[39m\n \u001b[90m 69 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台访问验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n\n\n\u001b[0m \u001b[90m 65 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 68 |\u001b[39m\n \u001b[90m 69 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台访问验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:67:46\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:17.986Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-4c2f12386e974cd98bac", "file": "basic-flow.spec.js", "line": 52, "column": 3}, {"title": "用户可以创建新学科", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 9535, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533640052\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533640052\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:97:52", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m\n \u001b[90m  96 |\u001b[39m     \u001b[90m// 验证学科出现在列表中\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m\n \u001b[90m  99 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 创建学科功能验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533640052\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n\n\u001b[0m \u001b[90m  95 |\u001b[39m\n \u001b[90m  96 |\u001b[39m     \u001b[90m// 验证学科出现在列表中\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m\n \u001b[90m  99 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 创建学科功能验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:97:52\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:17.987Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-49294912e935d17bb8e1", "file": "basic-flow.spec.js", "line": 72, "column": 3}, {"title": "用户可以删除学科", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 9363, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533639928\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533639928\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:116:52", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m\n \u001b[90m 115 |\u001b[39m     \u001b[90m// 验证学科已创建\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[90m// 找到删除按钮并点击\u001b[39m\n \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m deleteButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`tr:has-text(\"${subjectName}\") button:has-text(\"删除\")`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533639928\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n\n\u001b[0m \u001b[90m 114 |\u001b[39m\n \u001b[90m 115 |\u001b[39m     \u001b[90m// 验证学科已创建\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[90m// 找到删除按钮并点击\u001b[39m\n \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m deleteButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`tr:has-text(\"${subjectName}\") button:has-text(\"删除\")`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:116:52\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:18.164Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-515ef00d9c6c039021c6", "file": "basic-flow.spec.js", "line": 102, "column": 3}, {"title": "完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 15102, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:161:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}, "snippet": "\u001b[0m \u001b[90m 159 |\u001b[39m\n \u001b[90m 160 |\u001b[39m     \u001b[90m// 5. 刷新首页数据\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 161 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 163 |\u001b[39m\n \u001b[90m 164 |\u001b[39m     \u001b[90m// 6. 验证新创建的学科出现在首页\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 159 |\u001b[39m\n \u001b[90m 160 |\u001b[39m     \u001b[90m// 5. 刷新首页数据\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 161 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 163 |\u001b[39m\n \u001b[90m 164 |\u001b[39m     \u001b[90m// 6. 验证新创建的学科出现在首页\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:161:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:18.136Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-1dfd0090ec2d51505e88", "file": "basic-flow.spec.js", "line": 139, "column": 3}, {"title": "API响应时间性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 7193, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:185:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}, "snippet": "\u001b[0m \u001b[90m 183 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mconst\u001b[39m listStartTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.ant-table-tbody'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m5000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 186 |\u001b[39m     \u001b[36mconst\u001b[39m listLoadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m listStartTime\u001b[33m;\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// 验证列表加载时间小于5秒\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}, "message": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 183 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mconst\u001b[39m listStartTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.ant-table-tbody'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m5000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 186 |\u001b[39m     \u001b[36mconst\u001b[39m listLoadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m listStartTime\u001b[33m;\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// 验证列表加载时间小于5秒\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:185:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:21.525Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-dfa569daec7db11533cc", "file": "basic-flow.spec.js", "line": 170, "column": 3}, {"title": "用户可以访问首页并查看学科列表", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "failed", "duration": 7751, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:31:73", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[90m// 验证刷新按钮（在卡片的extra区域）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 首页访问和基本结构验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 34 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[90m// 验证刷新按钮（在卡片的extra区域）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 首页访问和基本结构验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 34 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:31:73\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:47.066Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-00758266ed204d3bae71", "file": "basic-flow.spec.js", "line": 11, "column": 3}, {"title": "用户可以执行健康检查", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "passed", "duration": 5559, "errors": [], "stdout": [{"text": "✅ 健康检查功能验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:47.413Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a6c0d510a7f2fa3549e7-640163dd11cb1b2b327b", "file": "basic-flow.spec.js", "line": 36, "column": 3}, {"title": "用户可以访问管理后台", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "failed", "duration": 8753, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:67:46", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}, "snippet": "\u001b[0m \u001b[90m 65 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 68 |\u001b[39m\n \u001b[90m 69 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台访问验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n\n\n\u001b[0m \u001b[90m 65 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 68 |\u001b[39m\n \u001b[90m 69 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台访问验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:67:46\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:47.837Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-5b8b94a713825c2510b6", "file": "basic-flow.spec.js", "line": 52, "column": 3}, {"title": "用户可以创建新学科", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 18, "parallelIndex": 2, "status": "failed", "duration": 10848, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533678904\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533678904\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:97:52", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m\n \u001b[90m  96 |\u001b[39m     \u001b[90m// 验证学科出现在列表中\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m\n \u001b[90m  99 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 创建学科功能验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533678904\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n\n\u001b[0m \u001b[90m  95 |\u001b[39m\n \u001b[90m  96 |\u001b[39m     \u001b[90m// 验证学科出现在列表中\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m\n \u001b[90m  99 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 创建学科功能验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:97:52\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:54.415Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-9fd44688a1360087925b", "file": "basic-flow.spec.js", "line": 72, "column": 3}, {"title": "用户可以删除学科", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 17, "parallelIndex": 5, "status": "failed", "duration": 10568, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533677927\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533677927\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:116:52", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m\n \u001b[90m 115 |\u001b[39m     \u001b[90m// 验证学科已创建\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[90m// 找到删除按钮并点击\u001b[39m\n \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m deleteButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`tr:has-text(\"${subjectName}\") button:has-text(\"删除\")`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533677927\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n\n\u001b[0m \u001b[90m 114 |\u001b[39m\n \u001b[90m 115 |\u001b[39m     \u001b[90m// 验证学科已创建\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[90m// 找到删除按钮并点击\u001b[39m\n \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m deleteButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`tr:has-text(\"${subjectName}\") button:has-text(\"删除\")`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:116:52\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:54.394Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-644fd06a010393182e25", "file": "basic-flow.spec.js", "line": 102, "column": 3}, {"title": "完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "failed", "duration": 14854, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:161:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}, "snippet": "\u001b[0m \u001b[90m 159 |\u001b[39m\n \u001b[90m 160 |\u001b[39m     \u001b[90m// 5. 刷新首页数据\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 161 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 163 |\u001b[39m\n \u001b[90m 164 |\u001b[39m     \u001b[90m// 6. 验证新创建的学科出现在首页\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 159 |\u001b[39m\n \u001b[90m 160 |\u001b[39m     \u001b[90m// 5. 刷新首页数据\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 161 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 163 |\u001b[39m\n \u001b[90m 164 |\u001b[39m     \u001b[90m// 6. 验证新创建的学科出现在首页\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:161:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:54.992Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-36f424bcb83d81012a72", "file": "basic-flow.spec.js", "line": 139, "column": 3}, {"title": "API响应时间性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 19, "parallelIndex": 4, "status": "failed", "duration": 9018, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:185:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}, "snippet": "\u001b[0m \u001b[90m 183 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mconst\u001b[39m listStartTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.ant-table-tbody'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m5000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 186 |\u001b[39m     \u001b[36mconst\u001b[39m listLoadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m listStartTime\u001b[33m;\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// 验证列表加载时间小于5秒\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}, "message": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 183 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mconst\u001b[39m listStartTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.ant-table-tbody'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m5000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 186 |\u001b[39m     \u001b[36mconst\u001b[39m listLoadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m listStartTime\u001b[33m;\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// 验证列表加载时间小于5秒\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:185:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:59.813Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-eefecb300c17ed024e84", "file": "basic-flow.spec.js", "line": 170, "column": 3}, {"title": "用户可以访问首页并查看学科列表", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 28, "parallelIndex": 5, "status": "failed", "duration": 7857, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:31:73", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[90m// 验证刷新按钮（在卡片的extra区域）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 首页访问和基本结构验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 34 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-card-extra button:has-text(\"刷新\")')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[90m// 验证刷新按钮（在卡片的extra区域）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 首页访问和基本结构验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 34 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:31:73\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:29.991Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问首页并查看学科列表-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 73, "line": 31}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-9ff31e6a9ec9461488b7", "file": "basic-flow.spec.js", "line": 11, "column": 3}, {"title": "用户可以执行健康检查", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 29, "parallelIndex": 2, "status": "passed", "duration": 4016, "errors": [], "stdout": [{"text": "✅ 健康检查功能验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:32.880Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a6c0d510a7f2fa3549e7-867bde68285b3679b25a", "file": "basic-flow.spec.js", "line": 36, "column": 3}, {"title": "用户可以访问管理后台", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 30, "parallelIndex": 4, "status": "failed", "duration": 7590, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:67:46", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}, "snippet": "\u001b[0m \u001b[90m 65 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 68 |\u001b[39m\n \u001b[90m 69 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台访问验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.ant-table')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table')\u001b[22m\n\n\n\u001b[0m \u001b[90m 65 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 68 |\u001b[39m\n \u001b[90m 69 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台访问验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:67:46\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:33.779Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以访问管理后台-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 46, "line": 67}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-ea0b4f5bbd90f958d930", "file": "basic-flow.spec.js", "line": 52, "column": 3}, {"title": "用户可以创建新学科", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 31, "parallelIndex": 1, "status": "failed", "duration": 11636, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533720474\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533720474\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:97:52", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m\n \u001b[90m  96 |\u001b[39m     \u001b[90m// 验证学科出现在列表中\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m\n \u001b[90m  99 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 创建学科功能验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"测试学科_1754533720474\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n\n\u001b[0m \u001b[90m  95 |\u001b[39m\n \u001b[90m  96 |\u001b[39m     \u001b[90m// 验证学科出现在列表中\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m\n \u001b[90m  99 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 创建学科功能验证通过'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:97:52\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:38.669Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以创建新学科-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 97}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-b623857ac2e9c7f1a544", "file": "basic-flow.spec.js", "line": 72, "column": 3}, {"title": "用户可以删除学科", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 29, "parallelIndex": 2, "status": "failed", "duration": 11721, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533718568\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533718568\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:116:52", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m\n \u001b[90m 115 |\u001b[39m     \u001b[90m// 验证学科已创建\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[90m// 找到删除按钮并点击\u001b[39m\n \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m deleteButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`tr:has-text(\"${subjectName}\") button:has-text(\"删除\")`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.ant-table-tbody')\nExpected string: \u001b[32m\"待删除学科_1754533718568\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.ant-table-tbody')\u001b[22m\n\n\n\u001b[0m \u001b[90m 114 |\u001b[39m\n \u001b[90m 115 |\u001b[39m     \u001b[90m// 验证学科已创建\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-table-tbody'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(subjectName)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m\n \u001b[90m 118 |\u001b[39m     \u001b[90m// 找到删除按钮并点击\u001b[39m\n \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m deleteButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`tr:has-text(\"${subjectName}\") button:has-text(\"删除\")`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:116:52\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:37.009Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-用户可以删除学科-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 52, "line": 116}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-000cdd37255ded5335d7", "file": "basic-flow.spec.js", "line": 102, "column": 3}, {"title": "完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 32, "parallelIndex": 3, "status": "failed", "duration": 17881, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:161:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}, "snippet": "\u001b[0m \u001b[90m 159 |\u001b[39m\n \u001b[90m 160 |\u001b[39m     \u001b[90m// 5. 刷新首页数据\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 161 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 163 |\u001b[39m\n \u001b[90m 164 |\u001b[39m     \u001b[90m// 6. 验证新创建的学科出现在首页\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-card-extra button:has-text(\"刷新\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 159 |\u001b[39m\n \u001b[90m 160 |\u001b[39m     \u001b[90m// 5. 刷新首页数据\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 161 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-card-extra button:has-text(\"刷新\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 162 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 163 |\u001b[39m\n \u001b[90m 164 |\u001b[39m     \u001b[90m// 6. 验证新创建的学科出现在首页\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:161:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:40.283Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-完整用户流程：首页---管理后台---创建学科---返回首页-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 161}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-f03fc7223fe40f1d0d1a", "file": "basic-flow.spec.js", "line": 139, "column": 3}, {"title": "API响应时间性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 33, "parallelIndex": 5, "status": "failed", "duration": 9007, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:185:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}, "snippet": "\u001b[0m \u001b[90m 183 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mconst\u001b[39m listStartTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.ant-table-tbody'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m5000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 186 |\u001b[39m     \u001b[36mconst\u001b[39m listLoadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m listStartTime\u001b[33m;\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// 验证列表加载时间小于5秒\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}, "message": "TimeoutError: page.waitForSelector: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-table-tbody') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 183 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 184 |\u001b[39m     \u001b[36mconst\u001b[39m listStartTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 185 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.ant-table-tbody'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m5000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 186 |\u001b[39m     \u001b[36mconst\u001b[39m listLoadTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m listStartTime\u001b[33m;\u001b[39m\n \u001b[90m 187 |\u001b[39m\n \u001b[90m 188 |\u001b[39m     \u001b[90m// 验证列表加载时间小于5秒\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js:185:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:40.666Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\basic-flow-期末复习平台---基础功能流程-API响应时间性能测试-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\basic-flow.spec.js", "column": 16, "line": 185}}], "status": "unexpected"}], "id": "a6c0d510a7f2fa3549e7-31ff12a535a5e0601cec", "file": "basic-flow.spec.js", "line": 170, "column": 3}, {"title": "用户可以访问首页并查看学科列表", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-4a0a7343fe0da5b28bc7", "file": "basic-flow.spec.js", "line": 11, "column": 3}, {"title": "用户可以执行健康检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-b8e66781b847ad55cf06", "file": "basic-flow.spec.js", "line": 36, "column": 3}, {"title": "用户可以访问管理后台", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-0fc9160538e87b12f5d3", "file": "basic-flow.spec.js", "line": 52, "column": 3}, {"title": "用户可以创建新学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-aa2aaa6f36f500b3ee8b", "file": "basic-flow.spec.js", "line": 72, "column": 3}, {"title": "用户可以删除学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-e8444c3334e89d6aeab3", "file": "basic-flow.spec.js", "line": 102, "column": 3}, {"title": "完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-35e99340268aa66b5918", "file": "basic-flow.spec.js", "line": 139, "column": 3}, {"title": "API响应时间性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-88cb4912be657b2e71ce", "file": "basic-flow.spec.js", "line": 170, "column": 3}, {"title": "用户可以访问首页并查看学科列表", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-e8d77ea3f93fc1f6fb34", "file": "basic-flow.spec.js", "line": 11, "column": 3}, {"title": "用户可以执行健康检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-6c266e9838493b08bf9a", "file": "basic-flow.spec.js", "line": 36, "column": 3}, {"title": "用户可以访问管理后台", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-6bda431f50d573b42da7", "file": "basic-flow.spec.js", "line": 52, "column": 3}, {"title": "用户可以创建新学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-7444e2ed2ac0f67fc69d", "file": "basic-flow.spec.js", "line": 72, "column": 3}, {"title": "用户可以删除学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-dd2c8151141a9693a6e9", "file": "basic-flow.spec.js", "line": 102, "column": 3}, {"title": "完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-b305691c3f6fd23d5943", "file": "basic-flow.spec.js", "line": 139, "column": 3}, {"title": "API响应时间性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-4e75f8e425de3a0bca4a", "file": "basic-flow.spec.js", "line": 170, "column": 3}, {"title": "用户可以访问首页并查看学科列表", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-84e78b5035e776e872eb", "file": "basic-flow.spec.js", "line": 11, "column": 3}, {"title": "用户可以执行健康检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-6152402a317e15fd44d8", "file": "basic-flow.spec.js", "line": 36, "column": 3}, {"title": "用户可以访问管理后台", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-750f7e6ccc620bb5950f", "file": "basic-flow.spec.js", "line": 52, "column": 3}, {"title": "用户可以创建新学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-7a123ffd621abc277587", "file": "basic-flow.spec.js", "line": 72, "column": 3}, {"title": "用户可以删除学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-0ddb94d948788f3ac54d", "file": "basic-flow.spec.js", "line": 102, "column": 3}, {"title": "完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-ae88fe7903e901eb4d98", "file": "basic-flow.spec.js", "line": 139, "column": 3}, {"title": "API响应时间性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-6b79a61697c8eb5e7058", "file": "basic-flow.spec.js", "line": 170, "column": 3}, {"title": "用户可以访问首页并查看学科列表", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-fc2979fee5eddc9778ba", "file": "basic-flow.spec.js", "line": 11, "column": 3}, {"title": "用户可以执行健康检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-9c6c60633a3e8eaa26c3", "file": "basic-flow.spec.js", "line": 36, "column": 3}, {"title": "用户可以访问管理后台", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-65490a621e56df2480ca", "file": "basic-flow.spec.js", "line": 52, "column": 3}, {"title": "用户可以创建新学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-46d8b6f9fa704ed947a0", "file": "basic-flow.spec.js", "line": 72, "column": 3}, {"title": "用户可以删除学科", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-74e091325077fb427cd4", "file": "basic-flow.spec.js", "line": 102, "column": 3}, {"title": "完整用户流程：首页 -> 管理后台 -> 创建学科 -> 返回首页", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-5ecf7911aab8f8e32e93", "file": "basic-flow.spec.js", "line": 139, "column": 3}, {"title": "API响应时间性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "a6c0d510a7f2fa3549e7-894cfd113daf733c139d", "file": "basic-flow.spec.js", "line": 170, "column": 3}]}]}, {"title": "performance.spec.js", "file": "performance.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "性能测试套件", "file": "performance.spec.js", "line": 4, "column": 6, "specs": [{"title": "页面加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "passed", "duration": 2132, "errors": [], "stdout": [{"text": "⚡ 开始页面加载性能测试\n"}, {"text": "🏠 首页加载时间: 1046ms\n"}, {"text": "⚙️ 管理后台加载时间: 856ms\n"}, {"text": "✅ 页面加载性能测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:26.342Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0395ec5c307b4a8c0388-0bfafc044d3e8d1d3e3f", "file": "performance.spec.js", "line": 10, "column": 3}, {"title": "API响应性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "failed", "duration": 15663, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:57:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}, "snippet": "\u001b[0m \u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 56 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m     \u001b[36mconst\u001b[39m createResponseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m createStartTime\u001b[33m;\u001b[39m\n \u001b[90m 60 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 56 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m     \u001b[36mconst\u001b[39m createResponseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m createStartTime\u001b[33m;\u001b[39m\n \u001b[90m 60 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:57:16\u001b[22m"}], "stdout": [{"text": "🔌 开始API响应性能测试\n"}, {"text": "📋 学科列表API响应时间: 874ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:26.741Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-a93ba4bae8347a9e6c7b", "file": "performance.spec.js", "line": 34, "column": 3}, {"title": "大量数据性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 4, "status": "failed", "duration": 14775, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:96:18", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}, "snippet": "\u001b[0m \u001b[90m 94 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`批量测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'textarea[placeholder=\"请输入学科描述（可选）\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`这是第${i}个批量测试学科的描述`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 96 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 97 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 98 |\u001b[39m     }\n \u001b[90m 99 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 94 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`批量测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'textarea[placeholder=\"请输入学科描述（可选）\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`这是第${i}个批量测试学科的描述`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 96 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 97 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 98 |\u001b[39m     }\n \u001b[90m 99 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:96:18\u001b[22m"}], "stdout": [{"text": "📊 开始大量数据性能测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:29.920Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-bc5b7f597e9124e4edd5", "file": "performance.spec.js", "line": 78, "column": 3}, {"title": "内存使用性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 3, "status": "failed", "duration": 14875, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:154:18", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}, "snippet": "\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`内存测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 156 |\u001b[39m     }\n \u001b[90m 157 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`内存测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 156 |\u001b[39m     }\n \u001b[90m 157 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:154:18\u001b[22m"}], "stdout": [{"text": "🧠 开始内存使用性能测试\n"}, {"text": "🔍 初始内存使用: 18MB\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:30.059Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-1c19c4a727860fc677d1", "file": "performance.spec.js", "line": 130, "column": 3}, {"title": "网络请求性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 16224, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:223:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}, "snippet": "\u001b[0m \u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m)\u001b[33m.\u001b[39mblur()\u001b[33m;\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 225 |\u001b[39m\n \u001b[90m 226 |\u001b[39m     \u001b[90m// 分析网络请求性能\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m)\u001b[33m.\u001b[39mblur()\u001b[33m;\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 225 |\u001b[39m\n \u001b[90m 226 |\u001b[39m     \u001b[90m// 分析网络请求性能\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:223:16\u001b[22m"}], "stdout": [{"text": "🌐 开始网络请求性能测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:28.638Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-95010ba9245e35135948", "file": "performance.spec.js", "line": 185, "column": 3}, {"title": "页面加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "passed", "duration": 4056, "errors": [], "stdout": [{"text": "⚡ 开始页面加载性能测试\n"}, {"text": "🏠 首页加载时间: 1673ms\n"}, {"text": "⚙️ 管理后台加载时间: 1236ms\n"}, {"text": "✅ 页面加载性能测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:00.566Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0395ec5c307b4a8c0388-2735103b8ebe326ad8e5", "file": "performance.spec.js", "line": 10, "column": 3}, {"title": "API响应性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "failed", "duration": 18436, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:57:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}, "snippet": "\u001b[0m \u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 56 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m     \u001b[36mconst\u001b[39m createResponseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m createStartTime\u001b[33m;\u001b[39m\n \u001b[90m 60 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 56 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m     \u001b[36mconst\u001b[39m createResponseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m createStartTime\u001b[33m;\u001b[39m\n \u001b[90m 60 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:57:16\u001b[22m"}], "stdout": [{"text": "🔌 开始API响应性能测试\n"}, {"text": "📋 学科列表API响应时间: 1896ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:05.102Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-04f0926a724bb8b1c0ed", "file": "performance.spec.js", "line": 34, "column": 3}, {"title": "大量数据性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "failed", "duration": 15247, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:96:18", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}, "snippet": "\u001b[0m \u001b[90m 94 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`批量测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'textarea[placeholder=\"请输入学科描述（可选）\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`这是第${i}个批量测试学科的描述`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 96 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 97 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 98 |\u001b[39m     }\n \u001b[90m 99 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 94 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`批量测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'textarea[placeholder=\"请输入学科描述（可选）\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`这是第${i}个批量测试学科的描述`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 96 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 97 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 98 |\u001b[39m     }\n \u001b[90m 99 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:96:18\u001b[22m"}], "stdout": [{"text": "📊 开始大量数据性能测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:05.258Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-db3649a933253a43ebbc", "file": "performance.spec.js", "line": 78, "column": 3}, {"title": "内存使用性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 22, "parallelIndex": 5, "status": "failed", "duration": 16756, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:154:18", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}, "snippet": "\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`内存测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 156 |\u001b[39m     }\n \u001b[90m 157 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`内存测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 156 |\u001b[39m     }\n \u001b[90m 157 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:154:18\u001b[22m"}], "stdout": [{"text": "🧠 开始内存使用性能测试\n"}, {"text": "🔍 初始内存使用: 0MB\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:09.193Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-a4f809d4af05a1d10874", "file": "performance.spec.js", "line": 130, "column": 3}, {"title": "网络请求性能测试", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 23, "parallelIndex": 2, "status": "failed", "duration": 18348, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:223:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}, "snippet": "\u001b[0m \u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m)\u001b[33m.\u001b[39mblur()\u001b[33m;\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 225 |\u001b[39m\n \u001b[90m 226 |\u001b[39m     \u001b[90m// 分析网络请求性能\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m)\u001b[33m.\u001b[39mblur()\u001b[33m;\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 225 |\u001b[39m\n \u001b[90m 226 |\u001b[39m     \u001b[90m// 分析网络请求性能\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:223:16\u001b[22m"}], "stdout": [{"text": "🌐 开始网络请求性能测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:09.814Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}}], "status": "unexpected"}], "id": "0395ec5c307b4a8c0388-1783cb5108c4d51b36dd", "file": "performance.spec.js", "line": 185, "column": 3}, {"title": "页面加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 34, "parallelIndex": 4, "status": "passed", "duration": 3032, "errors": [], "stdout": [{"text": "⚡ 开始页面加载性能测试\n"}, {"text": "🏠 首页加载时间: 1261ms\n"}, {"text": "⚙️ 管理后台加载时间: 1148ms\n"}, {"text": "✅ 页面加载性能测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:44.021Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0395ec5c307b4a8c0388-b236f8dba1f3c093cabb", "file": "performance.spec.js", "line": 10, "column": 3}, {"title": "API响应性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 34, "parallelIndex": 4, "status": "interrupted", "duration": 15784, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:57:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}, "snippet": "\u001b[0m \u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 56 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m     \u001b[36mconst\u001b[39m createResponseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m createStartTime\u001b[33m;\u001b[39m\n \u001b[90m 60 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 55 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 56 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 59 |\u001b[39m     \u001b[36mconst\u001b[39m createResponseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m createStartTime\u001b[33m;\u001b[39m\n \u001b[90m 60 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:57:16\u001b[22m"}], "stdout": [{"text": "🔌 开始API响应性能测试\n"}, {"text": "📋 学科列表API响应时间: 1212ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:47.186Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-API响应性能测试-webkit\\video.webm"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 57}}], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-916cea602dabed20a392", "file": "performance.spec.js", "line": 34, "column": 3}, {"title": "大量数据性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 35, "parallelIndex": 0, "status": "interrupted", "duration": 12022, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:96:18", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}, "snippet": "\u001b[0m \u001b[90m 94 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`批量测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'textarea[placeholder=\"请输入学科描述（可选）\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`这是第${i}个批量测试学科的描述`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 96 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 97 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 98 |\u001b[39m     }\n \u001b[90m 99 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 94 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`批量测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 95 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'textarea[placeholder=\"请输入学科描述（可选）\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`这是第${i}个批量测试学科的描述`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 96 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 97 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 98 |\u001b[39m     }\n \u001b[90m 99 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:96:18\u001b[22m"}], "stdout": [{"text": "📊 开始大量数据性能测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:50.886Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-大量数据性能测试-webkit\\video.webm"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 96}}], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-35a5cc25c5860fe6702d", "file": "performance.spec.js", "line": 78, "column": 3}, {"title": "内存使用性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 36, "parallelIndex": 2, "status": "interrupted", "duration": 11635, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:154:18", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}, "snippet": "\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`内存测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 156 |\u001b[39m     }\n \u001b[90m 157 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 153 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m`内存测试学科${i}`\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 154 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 155 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 156 |\u001b[39m     }\n \u001b[90m 157 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:154:18\u001b[22m"}], "stdout": [{"text": "🧠 开始内存使用性能测试\n"}, {"text": "🔍 初始内存使用: 0MB\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:51.109Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-内存使用性能测试-webkit\\video.webm"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 18, "line": 154}}], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-3c1c3bb1a795d96377ac", "file": "performance.spec.js", "line": 130, "column": 3}, {"title": "网络请求性能测试", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 37, "parallelIndex": 5, "status": "interrupted", "duration": 10356, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:223:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}, "snippet": "\u001b[0m \u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m)\u001b[33m.\u001b[39mblur()\u001b[33m;\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 225 |\u001b[39m\n \u001b[90m 226 |\u001b[39m     \u001b[90m// 分析网络请求性能\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 221 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m)\u001b[33m.\u001b[39mblur()\u001b[33m;\u001b[39m\n \u001b[90m 222 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 223 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 224 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 225 |\u001b[39m\n \u001b[90m 226 |\u001b[39m     \u001b[90m// 分析网络请求性能\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js:223:16\u001b[22m"}], "stdout": [{"text": "🌐 开始网络请求性能测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:52.568Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\performance-性能测试套件-网络请求性能测试-webkit\\video.webm"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\performance.spec.js", "column": 16, "line": 223}}], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-77987db2293f70372de0", "file": "performance.spec.js", "line": 185, "column": 3}, {"title": "页面加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-5079e74c732442752ca2", "file": "performance.spec.js", "line": 10, "column": 3}, {"title": "API响应性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-9766362b40db0c6eb0b6", "file": "performance.spec.js", "line": 34, "column": 3}, {"title": "大量数据性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-4cd083bf6e159114bbad", "file": "performance.spec.js", "line": 78, "column": 3}, {"title": "内存使用性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-ec555dfd0696f1f0ddb3", "file": "performance.spec.js", "line": 130, "column": 3}, {"title": "网络请求性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-f207805409c332edce0b", "file": "performance.spec.js", "line": 185, "column": 3}, {"title": "页面加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-e92d002370dc5527e036", "file": "performance.spec.js", "line": 10, "column": 3}, {"title": "API响应性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-c2732ed96e6af19a4c48", "file": "performance.spec.js", "line": 34, "column": 3}, {"title": "大量数据性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-cd81e1701bd3b965b684", "file": "performance.spec.js", "line": 78, "column": 3}, {"title": "内存使用性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-e339f26e384e0e1e9964", "file": "performance.spec.js", "line": 130, "column": 3}, {"title": "网络请求性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-f377080cc3d845bf9a02", "file": "performance.spec.js", "line": 185, "column": 3}, {"title": "页面加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-86a3b9cdee12a297e931", "file": "performance.spec.js", "line": 10, "column": 3}, {"title": "API响应性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-15e9343c2fd80cd00e12", "file": "performance.spec.js", "line": 34, "column": 3}, {"title": "大量数据性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-0dfae3cc1580cace7845", "file": "performance.spec.js", "line": 78, "column": 3}, {"title": "内存使用性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-e9f9aa2113d7d6f1f21d", "file": "performance.spec.js", "line": 130, "column": 3}, {"title": "网络请求性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-100f9b927cc99e06f7da", "file": "performance.spec.js", "line": 185, "column": 3}, {"title": "页面加载性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-8fec048423779922fb0b", "file": "performance.spec.js", "line": 10, "column": 3}, {"title": "API响应性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-c3712fad235eb0852dce", "file": "performance.spec.js", "line": 34, "column": 3}, {"title": "大量数据性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-11a9f9a81dddae7861a0", "file": "performance.spec.js", "line": 78, "column": 3}, {"title": "内存使用性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-4aeeab51f661c4d7e41a", "file": "performance.spec.js", "line": 130, "column": 3}, {"title": "网络请求性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "0395ec5c307b4a8c0388-5dd3b348cbff297a290f", "file": "performance.spec.js", "line": 185, "column": 3}]}]}, {"title": "simple-test.spec.js", "file": "simple-test.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "简单功能验证", "file": "simple-test.spec.js", "line": 4, "column": 6, "specs": [{"title": "验证前后端连接正常", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "passed", "duration": 3059, "errors": [], "stdout": [{"text": "✅ 前后端连接验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:30.769Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-56101c7bc28db39c2474", "file": "simple-test.spec.js", "line": 6, "column": 3}, {"title": "验证管理后台访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 5, "status": "passed", "duration": 1085, "errors": [], "stdout": [{"text": "✅ 管理后台访问验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:35.666Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-dedb866d99f64ff85350", "file": "simple-test.spec.js", "line": 27, "column": 3}, {"title": "验证API响应性能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "passed", "duration": 1908, "errors": [], "stdout": [{"text": "✅ API响应性能验证通过 - 响应时间: 1422ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:34.043Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-e606c48aa43ea2bf0a17", "file": "simple-test.spec.js", "line": 42, "column": 3}, {"title": "验证前后端连接正常", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 24, "parallelIndex": 4, "status": "passed", "duration": 4865, "errors": [], "stdout": [{"text": "✅ 前后端连接验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:14.100Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-d0deaaf9bacdce6a9faa", "file": "simple-test.spec.js", "line": 6, "column": 3}, {"title": "验证管理后台访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 25, "parallelIndex": 3, "status": "passed", "duration": 2811, "errors": [], "stdout": [{"text": "✅ 管理后台访问验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:14.238Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-7921c9dd423bed157143", "file": "simple-test.spec.js", "line": 27, "column": 3}, {"title": "验证API响应性能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 25, "parallelIndex": 3, "status": "passed", "duration": 2263, "errors": [], "stdout": [{"text": "✅ API响应性能验证通过 - 响应时间: 1390ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:18.184Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-a602d2ca77c5a96fe813", "file": "simple-test.spec.js", "line": 42, "column": 3}, {"title": "验证前后端连接正常", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 38, "parallelIndex": 1, "status": "passed", "duration": 4275, "errors": [], "stdout": [{"text": "✅ 前后端连接验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:53.276Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-b5323347117ebffac2ba", "file": "simple-test.spec.js", "line": 6, "column": 3}, {"title": "验证管理后台访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 38, "parallelIndex": 1, "status": "passed", "duration": 2294, "errors": [], "stdout": [{"text": "✅ 管理后台访问验证通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:57.668Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "bb9f026ced01f7d22821-ddb2badff2ab837fabf7", "file": "simple-test.spec.js", "line": 27, "column": 3}, {"title": "验证API响应性能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 39, "parallelIndex": 3, "status": "interrupted", "duration": 1535, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"健康检查\")')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"button\" data-v-9b48b94e=\"\" class=\"css-dev-only-do-not-override-10npqub ant-btn ant-btn-primary\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"健康检查\")')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"button\" data-v-9b48b94e=\"\" class=\"css-dev-only-do-not-override-10npqub ant-btn ant-btn-primary\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\simple-test.spec.js:47:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\simple-test.spec.js", "column": 16, "line": 47}, "snippet": "\u001b[0m \u001b[90m 45 |\u001b[39m     \u001b[90m// 测试健康检查API响应时间\u001b[39m\n \u001b[90m 46 |\u001b[39m     \u001b[36mconst\u001b[39m startTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 47 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"健康检查\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[36mconst\u001b[39m responseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\simple-test.spec.js", "column": 16, "line": 47}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"健康检查\")')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"button\" data-v-9b48b94e=\"\" class=\"css-dev-only-do-not-override-10npqub ant-btn ant-btn-primary\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\n\n\u001b[0m \u001b[90m 45 |\u001b[39m     \u001b[90m// 测试健康检查API响应时间\u001b[39m\n \u001b[90m 46 |\u001b[39m     \u001b[36mconst\u001b[39m startTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 47 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"健康检查\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[36mconst\u001b[39m responseTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\simple-test.spec.js:47:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:29:00.651Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\simple-test-简单功能验证-验证API响应性能-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\simple-test-简单功能验证-验证API响应性能-webkit\\video.webm"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\simple-test.spec.js", "column": 16, "line": 47}}], "status": "skipped"}], "id": "bb9f026ced01f7d22821-48139990532891b16225", "file": "simple-test.spec.js", "line": 42, "column": 3}, {"title": "验证前后端连接正常", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-63312bda5477abb705f7", "file": "simple-test.spec.js", "line": 6, "column": 3}, {"title": "验证管理后台访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-90c89f023d07c1bd7c52", "file": "simple-test.spec.js", "line": 27, "column": 3}, {"title": "验证API响应性能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-93ee185f0a01355ec25b", "file": "simple-test.spec.js", "line": 42, "column": 3}, {"title": "验证前后端连接正常", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-ee734bb1f18dcb4b3583", "file": "simple-test.spec.js", "line": 6, "column": 3}, {"title": "验证管理后台访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-a23ae6045d5b7f267110", "file": "simple-test.spec.js", "line": 27, "column": 3}, {"title": "验证API响应性能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-e8133d99a9800b8dadd0", "file": "simple-test.spec.js", "line": 42, "column": 3}, {"title": "验证前后端连接正常", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-ae76cac3d55e500b324c", "file": "simple-test.spec.js", "line": 6, "column": 3}, {"title": "验证管理后台访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-6ed57f044227992ef53f", "file": "simple-test.spec.js", "line": 27, "column": 3}, {"title": "验证API响应性能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-a05eca8615f2097964c9", "file": "simple-test.spec.js", "line": 42, "column": 3}, {"title": "验证前后端连接正常", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-950065bb05c7e3078593", "file": "simple-test.spec.js", "line": 6, "column": 3}, {"title": "验证管理后台访问", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-c0001e379bb3916d1140", "file": "simple-test.spec.js", "line": 27, "column": 3}, {"title": "验证API响应性能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "bb9f026ced01f7d22821-9dfc279a23d92d440545", "file": "simple-test.spec.js", "line": 42, "column": 3}]}]}, {"title": "subject-management.spec.js", "file": "subject-management.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "学科管理完整功能测试", "file": "subject-management.spec.js", "line": 4, "column": 6, "specs": [{"title": "完整的学科管理流程 - 创建、编辑、删除", "ok": false, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "failed", "duration": 9768, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[value=\"card\"]')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[value=\"card\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input checked type=\"radio\" value=\"card\" class=\"ant-radio-button-input\"/>\u001b[22m\n\u001b[2m      - unexpected value \"hidden\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[value=\"card\"]')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[value=\"card\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input checked type=\"radio\" value=\"card\" class=\"ant-radio-button-input\"/>\u001b[22m\n\u001b[2m      - unexpected value \"hidden\"\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:29:55", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 55, "line": 29}, "snippet": "\u001b[0m \u001b[90m 27 |\u001b[39m     \n \u001b[90m 28 |\u001b[39m     \u001b[90m// 验证视图切换按钮\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"card\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"table\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m     \n \u001b[90m 32 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台页面加载验证通过'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 55, "line": 29}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[value=\"card\"]')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[value=\"card\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input checked type=\"radio\" value=\"card\" class=\"ant-radio-button-input\"/>\u001b[22m\n\u001b[2m      - unexpected value \"hidden\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 27 |\u001b[39m     \n \u001b[90m 28 |\u001b[39m     \u001b[90m// 验证视图切换按钮\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"card\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"table\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m     \n \u001b[90m 32 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台页面加载验证通过'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:29:55\u001b[22m"}], "stdout": [{"text": "🧪 开始学科管理完整流程测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:35.966Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 55, "line": 29}}], "status": "unexpected"}], "id": "399b51919a5300f9dd99-90ac1e2f579498faee0b", "file": "subject-management.spec.js", "line": 21, "column": 3}, {"title": "表单验证和错误处理测试", "ok": false, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 5, "status": "failed", "duration": 15125, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:151:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 151}, "snippet": "\u001b[0m \u001b[90m 149 |\u001b[39m     \n \u001b[90m 150 |\u001b[39m     \u001b[90m// 尝试提交空表单\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 151 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 152 |\u001b[39m     \n \u001b[90m 153 |\u001b[39m     \u001b[90m// 验证验证错误信息\u001b[39m\n \u001b[90m 154 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-form-item-explain-error:has-text(\"请输入学科名称\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 151}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 149 |\u001b[39m     \n \u001b[90m 150 |\u001b[39m     \u001b[90m// 尝试提交空表单\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 151 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 152 |\u001b[39m     \n \u001b[90m 153 |\u001b[39m     \u001b[90m// 验证验证错误信息\u001b[39m\n \u001b[90m 154 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-form-item-explain-error:has-text(\"请输入学科名称\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:151:16\u001b[22m"}], "stdout": [{"text": "🛡️ 开始表单验证和错误处理测试\n"}, {"text": "📝 测试必填字段验证\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:36.948Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-表单验证和错误处理测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 151}}], "status": "unexpected"}], "id": "399b51919a5300f9dd99-54fc794f501f5fc5446f", "file": "subject-management.spec.js", "line": 141, "column": 3}, {"title": "响应式设计测试", "ok": true, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 2, "status": "passed", "duration": 7766, "errors": [], "stdout": [{"text": "📱 开始响应式设计测试\n"}, {"text": "✅ 响应式设计测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:44.135Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "399b51919a5300f9dd99-fd09f0cee04ec5cc901f", "file": "subject-management.spec.js", "line": 206, "column": 3}, {"title": "性能测试", "ok": false, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "failed", "duration": 15765, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:259:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 259}, "snippet": "\u001b[0m \u001b[90m 257 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 258 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 259 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 260 |\u001b[39m     \n \u001b[90m 261 |\u001b[39m     \u001b[90m// 等待API响应\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 259}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 257 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 258 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 259 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 260 |\u001b[39m     \n \u001b[90m 261 |\u001b[39m     \u001b[90m// 等待API响应\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:259:16\u001b[22m"}], "stdout": [{"text": "⚡ 开始性能测试\n"}, {"text": "📊 页面加载时间: 1001ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:27:47.038Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-性能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-性能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-性能测试-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 259}}], "status": "unexpected"}], "id": "399b51919a5300f9dd99-6fca2e322013529bb118", "file": "subject-management.spec.js", "line": 243, "column": 3}, {"title": "完整的学科管理流程 - 创建、编辑、删除", "ok": false, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 24, "parallelIndex": 4, "status": "failed", "duration": 10634, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[value=\"card\"]')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[value=\"card\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input checked type=\"radio\" value=\"card\" class=\"ant-radio-button-input\"/>\u001b[22m\n\u001b[2m      - unexpected value \"hidden\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[value=\"card\"]')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[value=\"card\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input checked type=\"radio\" value=\"card\" class=\"ant-radio-button-input\"/>\u001b[22m\n\u001b[2m      - unexpected value \"hidden\"\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:29:55", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 55, "line": 29}, "snippet": "\u001b[0m \u001b[90m 27 |\u001b[39m     \n \u001b[90m 28 |\u001b[39m     \u001b[90m// 验证视图切换按钮\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"card\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"table\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m     \n \u001b[90m 32 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台页面加载验证通过'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 55, "line": 29}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[value=\"card\"]')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[value=\"card\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input checked type=\"radio\" value=\"card\" class=\"ant-radio-button-input\"/>\u001b[22m\n\u001b[2m      - unexpected value \"hidden\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 27 |\u001b[39m     \n \u001b[90m 28 |\u001b[39m     \u001b[90m// 验证视图切换按钮\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"card\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[value=\"table\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 31 |\u001b[39m     \n \u001b[90m 32 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ 管理后台页面加载验证通过'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:29:55\u001b[22m"}], "stdout": [{"text": "🧪 开始学科管理完整流程测试\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:19.673Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 55, "line": 29}}], "status": "unexpected"}], "id": "399b51919a5300f9dd99-92fe6545ed8e24a2578c", "file": "subject-management.spec.js", "line": 21, "column": 3}, {"title": "表单验证和错误处理测试", "ok": false, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 25, "parallelIndex": 3, "status": "failed", "duration": 15747, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:151:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 151}, "snippet": "\u001b[0m \u001b[90m 149 |\u001b[39m     \n \u001b[90m 150 |\u001b[39m     \u001b[90m// 尝试提交空表单\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 151 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 152 |\u001b[39m     \n \u001b[90m 153 |\u001b[39m     \u001b[90m// 验证验证错误信息\u001b[39m\n \u001b[90m 154 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-form-item-explain-error:has-text(\"请输入学科名称\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 151}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 149 |\u001b[39m     \n \u001b[90m 150 |\u001b[39m     \u001b[90m// 尝试提交空表单\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 151 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 152 |\u001b[39m     \n \u001b[90m 153 |\u001b[39m     \u001b[90m// 验证验证错误信息\u001b[39m\n \u001b[90m 154 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.ant-form-item-explain-error:has-text(\"请输入学科名称\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:151:16\u001b[22m"}], "stdout": [{"text": "🛡️ 开始表单验证和错误处理测试\n"}, {"text": "📝 测试必填字段验证\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:20.476Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-表单验证和错误处理测试-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 151}}], "status": "unexpected"}], "id": "399b51919a5300f9dd99-22024c3594515b5d1262", "file": "subject-management.spec.js", "line": 141, "column": 3}, {"title": "响应式设计测试", "ok": true, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "passed", "duration": 10481, "errors": [], "stdout": [{"text": "📱 开始响应式设计测试\n"}, {"text": "✅ 响应式设计测试通过\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:23.889Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "399b51919a5300f9dd99-5ea6f2d127c46c3f5b85", "file": "subject-management.spec.js", "line": 206, "column": 3}, {"title": "性能测试", "ok": false, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 27, "parallelIndex": 0, "status": "failed", "duration": 18373, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:259:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 259}, "snippet": "\u001b[0m \u001b[90m 257 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 258 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 259 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 260 |\u001b[39m     \n \u001b[90m 261 |\u001b[39m     \u001b[90m// 等待API响应\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 259}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.ant-modal button:has-text(\"创建\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 257 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"新建学科\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 258 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder=\"请输入学科名称\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'性能测试学科'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 259 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'.ant-modal button:has-text(\"创建\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 260 |\u001b[39m     \n \u001b[90m 261 |\u001b[39m     \u001b[90m// 等待API响应\u001b[39m\n \u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m100\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:259:16\u001b[22m"}], "stdout": [{"text": "⚡ 开始性能测试\n"}, {"text": "📊 页面加载时间: 1439ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:27.919Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-性能测试-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-性能测试-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-性能测试-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 259}}], "status": "unexpected"}], "id": "399b51919a5300f9dd99-f871304c5243b7b2a903", "file": "subject-management.spec.js", "line": 243, "column": 3}, {"title": "完整的学科管理流程 - 创建、编辑、删除", "ok": true, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 38, "parallelIndex": 1, "status": "interrupted", "duration": 2258, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"初始化数据库\")')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"button\" data-v-105e508c=\"\" class=\"css-dev-only-do-not-override-10npqub ant-btn ant-btn-default\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"初始化数据库\")')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"button\" data-v-105e508c=\"\" class=\"css-dev-only-do-not-override-10npqub ant-btn ant-btn-default\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\n    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:17:16", "location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 17}, "snippet": "\u001b[0m \u001b[90m 15 |\u001b[39m     \n \u001b[90m 16 |\u001b[39m     \u001b[90m// 初始化数据库（确保测试环境干净）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 20 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 17}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"初始化数据库\")')\u001b[22m\n\u001b[2m    - locator resolved to <button type=\"button\" data-v-105e508c=\"\" class=\"css-dev-only-do-not-override-10npqub ant-btn ant-btn-default\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m     \n \u001b[90m 16 |\u001b[39m     \u001b[90m// 初始化数据库（确保测试环境干净）\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button:has-text(\"初始化数据库\")'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m3000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 19 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 20 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js:17:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-07T02:28:59.988Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\ai\\Term Review\\test-results\\subject-management-学科管理完整功能测试-完整的学科管理流程---创建、编辑、删除-webkit\\video.webm"}], "errorLocation": {"file": "D:\\ai\\Term Review\\tests\\e2e\\subject-management.spec.js", "column": 16, "line": 17}}], "status": "skipped"}], "id": "399b51919a5300f9dd99-fd69415733e952a7f2c5", "file": "subject-management.spec.js", "line": 21, "column": 3}, {"title": "表单验证和错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-6cce6107d1ac0005d284", "file": "subject-management.spec.js", "line": 141, "column": 3}, {"title": "响应式设计测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-cf53e4ffee6295938214", "file": "subject-management.spec.js", "line": 206, "column": 3}, {"title": "性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-22bc1fd4c08f8ff2649d", "file": "subject-management.spec.js", "line": 243, "column": 3}, {"title": "完整的学科管理流程 - 创建、编辑、删除", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-90d3bd61bb0fa18e5e88", "file": "subject-management.spec.js", "line": 21, "column": 3}, {"title": "表单验证和错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-02d3db592429f4d6cbd5", "file": "subject-management.spec.js", "line": 141, "column": 3}, {"title": "响应式设计测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-288039d368ffb4b09297", "file": "subject-management.spec.js", "line": 206, "column": 3}, {"title": "性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-8b142e8da8009a179e8f", "file": "subject-management.spec.js", "line": 243, "column": 3}, {"title": "完整的学科管理流程 - 创建、编辑、删除", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-7dd52442e1dfeebf98ee", "file": "subject-management.spec.js", "line": 21, "column": 3}, {"title": "表单验证和错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-05d6f2ba15955eeef094", "file": "subject-management.spec.js", "line": 141, "column": 3}, {"title": "响应式设计测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-60dfe078be497514dffa", "file": "subject-management.spec.js", "line": 206, "column": 3}, {"title": "性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-ea93ac0d0c447cbfd88d", "file": "subject-management.spec.js", "line": 243, "column": 3}, {"title": "完整的学科管理流程 - 创建、编辑、删除", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-7b087e96a29cea3cd5c4", "file": "subject-management.spec.js", "line": 21, "column": 3}, {"title": "表单验证和错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-c533116148c467c69324", "file": "subject-management.spec.js", "line": 141, "column": 3}, {"title": "响应式设计测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-993c162c3fc9cce8e27c", "file": "subject-management.spec.js", "line": 206, "column": 3}, {"title": "性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-71fc025f0b3da2e6e5d2", "file": "subject-management.spec.js", "line": 243, "column": 3}, {"title": "完整的学科管理流程 - 创建、编辑、删除", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-2dcb5f5088b4aacb063b", "file": "subject-management.spec.js", "line": 21, "column": 3}, {"title": "表单验证和错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-8fe73c227ca3ac1cea21", "file": "subject-management.spec.js", "line": 141, "column": 3}, {"title": "响应式设计测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-5069c0f44aa6ecec6446", "file": "subject-management.spec.js", "line": 206, "column": 3}, {"title": "性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [], "status": "skipped"}], "id": "399b51919a5300f9dd99-3472b6236c62365dfbe8", "file": "subject-management.spec.js", "line": 243, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-07T02:26:57.275Z", "duration": 126292.13699999999, "expected": 16, "skipped": 85, "unexpected": 32, "flaky": 0}}