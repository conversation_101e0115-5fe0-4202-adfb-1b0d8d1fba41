import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import SubjectModal from '../SubjectModal.vue'
import type { Subject } from '@/services/api'

// Mock API Service
const mockApiService = {
  createSubject: vi.fn(),
  updateSubject: vi.fn(),
  checkSubjectName: vi.fn()
}

vi.mock('@/services/api', () => ({
  ApiService: mockApiService
}))

// Mock Ant Design Vue components
vi.mock('ant-design-vue', () => ({
  AModal: {
    name: 'AModal',
    props: ['open', 'title', 'okText', 'cancelText', 'confirmLoading', 'width'],
    emits: ['update:open', 'ok', 'cancel'],
    template: `
      <div v-if="open" class="ant-modal">
        <div class="ant-modal-title">{{ title }}</div>
        <slot></slot>
        <div class="ant-modal-footer">
          <button @click="$emit('cancel')">{{ cancelText }}</button>
          <button @click="$emit('ok')">{{ okText }}</button>
        </div>
      </div>
    `
  },
  AForm: {
    name: 'AForm',
    template: '<form class="ant-form"><slot></slot></form>'
  },
  AFormItem: {
    name: 'AFormItem',
    props: ['label', 'name'],
    template: '<div class="ant-form-item"><label>{{ label }}</label><slot></slot></div>'
  },
  AInput: {
    name: 'AInput',
    props: ['value', 'placeholder', 'maxlength', 'showCount'],
    emits: ['update:value', 'blur', 'input'],
    template: '<input :value="value" @input="$emit(\'update:value\', $event.target.value)" @blur="$emit(\'blur\')" />'
  },
  ATextarea: {
    name: 'ATextarea',
    props: ['value', 'placeholder', 'maxlength', 'rows', 'showCount'],
    emits: ['update:value'],
    template: '<textarea :value="value" @input="$emit(\'update:value\', $event.target.value)"></textarea>'
  },
  AAlert: {
    name: 'AAlert',
    props: ['message', 'type', 'showIcon', 'closable'],
    template: '<div class="ant-alert" :class="`ant-alert-${type}`">{{ message }}</div>'
  },
  ADivider: {
    name: 'ADivider',
    template: '<div class="ant-divider"><slot></slot></div>'
  },
  ADescriptions: {
    name: 'ADescriptions',
    props: ['column', 'size'],
    template: '<div class="ant-descriptions"><slot></slot></div>'
  },
  ADescriptionsItem: {
    name: 'ADescriptionsItem',
    props: ['label'],
    template: '<div class="ant-descriptions-item"><span class="label">{{ label }}</span><span class="content"><slot></slot></span></div>'
  },
  message: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

describe('SubjectModal', () => {
  const mockSubject: Subject = {
    id: 1,
    name: '数学',
    description: '高等数学、线性代数、概率论等数学相关内容',
    file_count: 5,
    total_size: 1024000,
    created_at: '2025-01-08T10:00:00.000Z',
    updated_at: '2025-01-08T12:00:00.000Z'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该在创建模式下正确渲染', () => {
    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'create'
      }
    })

    expect(wrapper.find('.ant-modal-title').text()).toBe('新建学科')
    expect(wrapper.text()).toContain('创建')
  })

  it('应该在编辑模式下正确渲染', () => {
    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'edit',
        subject: mockSubject
      }
    })

    expect(wrapper.find('.ant-modal-title').text()).toBe('编辑学科')
    expect(wrapper.text()).toContain('保存')
    expect(wrapper.text()).toContain('学科信息')
  })

  it('应该在编辑模式下预填充表单数据', async () => {
    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'edit',
        subject: mockSubject
      }
    })

    await nextTick()

    const nameInput = wrapper.find('input')
    const descriptionTextarea = wrapper.find('textarea')

    expect(nameInput.element.value).toBe('数学')
    expect(descriptionTextarea.element.value).toBe('高等数学、线性代数、概率论等数学相关内容')
  })

  it('应该处理名称输入和失焦事件', async () => {
    mockApiService.checkSubjectName.mockResolvedValue({
      success: true,
      data: { name: '新学科', available: true }
    })

    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'create'
      }
    })

    const nameInput = wrapper.find('input')
    
    // 输入名称
    await nameInput.setValue('新学科')
    await nameInput.trigger('input')
    
    // 失焦触发名称检查
    await nameInput.trigger('blur')
    await nextTick()

    expect(mockApiService.checkSubjectName).toHaveBeenCalledWith('新学科', undefined)
  })

  it('应该显示名称可用的提示', async () => {
    mockApiService.checkSubjectName.mockResolvedValue({
      success: true,
      data: { name: '新学科', available: true }
    })

    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'create'
      }
    })

    // 设置组件内部状态
    const vm = wrapper.vm as any
    vm.formData.name = '新学科'
    vm.nameCheckResult = { name: '新学科', available: true }
    
    await nextTick()

    expect(wrapper.text()).toContain('新学科')
    expect(wrapper.text()).toContain('可以使用')
    expect(wrapper.find('.ant-alert-success')).toBeTruthy()
  })

  it('应该显示名称不可用的提示', async () => {
    mockApiService.checkSubjectName.mockResolvedValue({
      success: true,
      data: { name: '数学', available: false, conflictId: 1 }
    })

    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'create'
      }
    })

    // 设置组件内部状态
    const vm = wrapper.vm as any
    vm.formData.name = '数学'
    vm.nameCheckResult = { name: '数学', available: false, conflictId: 1 }
    
    await nextTick()

    expect(wrapper.text()).toContain('数学')
    expect(wrapper.text()).toContain('已存在')
    expect(wrapper.find('.ant-alert-error')).toBeTruthy()
  })

  it('应该成功创建学科', async () => {
    const mockResponse = {
      success: true,
      data: { ...mockSubject, id: 2, name: '新学科' }
    }
    
    mockApiService.createSubject.mockResolvedValue(mockResponse)
    mockApiService.checkSubjectName.mockResolvedValue({
      success: true,
      data: { name: '新学科', available: true }
    })

    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'create'
      }
    })

    // 设置表单数据
    const vm = wrapper.vm as any
    vm.formData.name = '新学科'
    vm.formData.description = '新学科描述'
    vm.nameCheckResult = { name: '新学科', available: true }

    // 模拟表单验证通过
    vm.formRef = { validate: vi.fn().mockResolvedValue(true) }

    // 触发提交
    await wrapper.find('.ant-modal-footer button:last-child').trigger('click')
    await nextTick()

    expect(mockApiService.createSubject).toHaveBeenCalledWith({
      name: '新学科',
      description: '新学科描述'
    })
    expect(wrapper.emitted('success')).toBeTruthy()
  })

  it('应该成功更新学科', async () => {
    const mockResponse = {
      success: true,
      data: { ...mockSubject, name: '更新的数学' }
    }
    
    mockApiService.updateSubject.mockResolvedValue(mockResponse)
    mockApiService.checkSubjectName.mockResolvedValue({
      success: true,
      data: { name: '更新的数学', available: true }
    })

    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'edit',
        subject: mockSubject
      }
    })

    // 设置表单数据
    const vm = wrapper.vm as any
    vm.formData.name = '更新的数学'
    vm.formData.description = '更新的描述'
    vm.nameCheckResult = { name: '更新的数学', available: true }

    // 模拟表单验证通过
    vm.formRef = { validate: vi.fn().mockResolvedValue(true) }

    // 触发提交
    await wrapper.find('.ant-modal-footer button:last-child').trigger('click')
    await nextTick()

    expect(mockApiService.updateSubject).toHaveBeenCalledWith(1, {
      name: '更新的数学',
      description: '更新的描述'
    })
    expect(wrapper.emitted('success')).toBeTruthy()
  })

  it('应该处理取消操作', async () => {
    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'create'
      }
    })

    await wrapper.find('.ant-modal-footer button:first-child').trigger('click')

    expect(wrapper.emitted('cancel')).toBeTruthy()
    expect(wrapper.emitted('update:open')).toBeTruthy()
    expect(wrapper.emitted('update:open')?.[0]).toEqual([false])
  })

  it('应该在编辑模式下排除当前学科ID进行名称检查', async () => {
    mockApiService.checkSubjectName.mockResolvedValue({
      success: true,
      data: { name: '数学', available: true }
    })

    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'edit',
        subject: mockSubject
      }
    })

    const nameInput = wrapper.find('input')
    await nameInput.trigger('blur')
    await nextTick()

    expect(mockApiService.checkSubjectName).toHaveBeenCalledWith('数学', 1)
  })

  it('应该正确格式化文件大小', () => {
    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'edit',
        subject: mockSubject
      }
    })

    const vm = wrapper.vm as any
    expect(vm.formatFileSize(0)).toBe('0 B')
    expect(vm.formatFileSize(1024)).toBe('1.0 KB')
    expect(vm.formatFileSize(1048576)).toBe('1.0 MB')
    expect(vm.formatFileSize(1073741824)).toBe('1.0 GB')
  })

  it('应该正确格式化日期', () => {
    const wrapper = mount(SubjectModal, {
      props: {
        open: true,
        mode: 'edit',
        subject: mockSubject
      }
    })

    const vm = wrapper.vm as any
    const formattedDate = vm.formatDate('2025-01-08T10:00:00.000Z')
    
    expect(formattedDate).toContain('2025')
    expect(formattedDate).toContain('01')
    expect(formattedDate).toContain('08')
  })
})
