# 后端架构设计与开发指南

## 文档信息
- **版本**: v2.0.0
- **创建日期**: 2025-01-08
- **最后更新**: 2025-08-07
- **负责人**: Bob (架构师) & Alex (工程师)
- **状态**: Sprint-2 数据模型扩展完成

## 项目概述
期末复习平台后端系统架构设计文档，包含系统架构、技术选型、开发规范等内容。

### Sprint-2 更新内容
本次更新完成了数据模型扩展与优化，主要包括：
- 扩展学科数据模型，支持统计字段(file_count, total_size)
- 创建数据验证工具类SubjectValidator
- 优化数据库查询性能，添加唯一索引
- 建立双数据库支持体系(Mock + SQLite)

## 技术栈选型
基于Sprint-1需求和API契约，确定以下技术栈：

### 核心技术
- **编程语言**: Node.js 18+
- **Web框架**: Koa 2.14+ (轻量级、中间件机制、async/await支持)
- **数据库**: SQLite (sqlite3 5.1.7+) - 嵌入式数据库，适合中小规模应用
  - 开发/测试环境：Mock数据库 (内存数据库，快速测试)
  - 生产环境：真实SQLite数据库 (持久化存储)
- **文件上传**: @koa/multer 3.0+ (基于multer的Koa适配版本)
- **Markdown解析**: marked 12.0+ (轻量级、可扩展、标准兼容)
- **代码高亮**: highlight.js 11.9+ (语言支持广、主题丰富)

### 开发工具
- **版本控制**: Git
- **包管理**: npm
- **测试框架**: Jest 29.7+ (单元测试) + Supertest 6.3+ (API测试)
- **开发工具**: nodemon 3.0+ (热重载)
- **代码规范**: ESLint 8.54+
- **参数验证**: Joi 17.11+ (强大的数据验证库)

## 系统架构

### 整体架构图
> 待Bob补充架构图

### 模块划分
> 待根据PRD进行模块设计

#### 用户管理模块
- 用户注册/登录
- 权限管理
- 个人信息管理

#### 课程管理模块
- 课程信息管理
- 课程内容管理

#### 复习计划模块
- 计划创建与管理
- 进度跟踪

#### 学习记录模块
- 学习数据记录
- 统计分析

### 数据库设计

#### 数据库架构
采用SQLite嵌入式数据库，支持ACID事务，适合中小规模应用。

**双数据库支持策略**：
- **Mock数据库**: 用于开发和测试，内存存储，快速响应
- **SQLite数据库**: 用于生产环境，文件存储，数据持久化

#### 核心表结构

##### 1. subjects (学科表)
```sql
CREATE TABLE subjects (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                    -- 学科名称，1-50字符
  description TEXT,                      -- 学科描述，最大500字符
  file_count INTEGER DEFAULT 0,          -- 文件数量统计
  total_size INTEGER DEFAULT 0,          -- 总文件大小(字节)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 学科名称唯一索引
CREATE UNIQUE INDEX idx_subjects_name ON subjects(name);
```

##### 2. file_nodes (文件节点表)
```sql
CREATE TABLE file_nodes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  subject_id INTEGER NOT NULL,           -- 关联学科ID
  name TEXT NOT NULL,                    -- 文件/文件夹名称
  type TEXT NOT NULL CHECK(type IN ('file', 'folder')),
  file_size INTEGER DEFAULT 0,          -- 文件大小(字节)
  parent_id INTEGER,                     -- 父节点ID
  path TEXT NOT NULL,                    -- 文件路径
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES file_nodes(id) ON DELETE CASCADE
);

-- 文件节点索引
CREATE INDEX idx_file_nodes_subject_id ON file_nodes(subject_id);
```

##### 3. operation_logs (操作日志表)
```sql
CREATE TABLE operation_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  operation_type TEXT NOT NULL,         -- 操作类型
  table_name TEXT NOT NULL,             -- 操作表名
  record_id INTEGER,                    -- 记录ID
  old_data TEXT,                        -- 旧数据(JSON)
  new_data TEXT,                        -- 新数据(JSON)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 数据模型关系
- subjects (1) -> file_nodes (N): 一个学科包含多个文件节点
- file_nodes (1) -> file_nodes (N): 文件节点支持层级结构
- 所有表都有操作日志记录，支持数据审计

### 接口设计原则
- RESTful API设计规范
- 统一的响应格式
- 完善的错误处理
- 接口版本管理

## 开发规范

### 代码规范
> 待Alex根据技术栈补充具体规范

### 目录结构 (Sprint-2 更新)
```
backend/
├── src/
│   ├── app.js              # 应用入口文件
│   ├── controllers/        # 控制器层 (暂未实现)
│   ├── services/           # 业务逻辑层 (暂未实现)
│   ├── models/             # 数据模型层
│   │   └── Subject.js      # 学科数据模型 (已扩展统计功能)
│   ├── middleware/         # 中间件
│   │   ├── errorHandler.js # 全局错误处理中间件
│   │   ├── logger.js       # 请求日志中间件
│   │   └── validation.js   # 参数验证中间件 (支持学科验证)
│   ├── routes/             # 路由层
│   │   ├── health.js       # 健康检查路由
│   │   ├── subjects.js     # 学科管理路由 (待扩展PUT/GET check-name)
│   │   └── dev.js          # 开发专用路由
│   ├── config/             # 配置文件
│   │   └── database.js     # 数据库配置和管理 (支持双数据库)
│   ├── scripts/            # 脚本文件
│   │   └── init-database.js # 数据库初始化脚本
│   └── utils/              # 工具函数
│       └── subjectValidator.js # 学科数据验证工具类 (新增)
├── data/                   # 数据目录 (运行时创建)
│   └── database.sqlite     # SQLite数据库文件
├── tests/                  # 测试目录
│   ├── integration/        # 集成测试
│   │   ├── health.test.js  # 健康检查API测试
│   │   └── subjects.test.js # 学科管理API测试
│   └── setup.js            # 测试环境配置
├── package.json            # 项目依赖配置
└── README.md               # 项目说明文档
```

## 数据模型设计

### Subject模型类功能
位置：`src/models/Subject.js`

#### 核心方法
- `findAll()`: 获取所有学科列表（包含统计字段）
- `findById(id)`: 根据ID查找学科（包含统计字段）
- `findByName(name)`: 根据名称查找学科
- `create(data)`: 创建新学科
- `update(id, data)`: 更新学科信息
- `delete(id)`: 删除学科（级联删除相关文件）
- `isNameAvailable(name, excludeId)`: 检查名称可用性

#### 统计功能方法 (Sprint-2 新增)
- `getStats(id)`: 获取学科详细统计信息
- `updateStats(id)`: 更新学科统计信息
- `updateAllStats()`: 批量更新所有学科统计信息
- `findByIdWithStats(id)`: 获取学科详情（含实时统计）
- `findAllWithStats()`: 获取所有学科（含实时统计）

#### 数据验证
使用`SubjectValidator`类进行数据验证：
- 学科名称：1-50字符，支持中英文、数字、空格、连字符、下划线
- 学科描述：最大500字符，可选
- 唯一性检查：确保学科名称在系统中唯一

### SubjectValidator验证工具类
位置：`src/utils/subjectValidator.js`

#### 验证方法
- `validateCreate(data)`: 验证创建学科数据
- `validateUpdate(data)`: 验证更新学科数据
- `validateCheckName(data)`: 验证名称检查数据
- `validateId(id)`: 验证学科ID
- `validateName(name)`: 验证学科名称格式
- `validateWithDetails(type, data)`: 获取详细验证结果

#### 验证规则
基于Joi库实现，支持：
- 数据类型验证
- 长度限制验证
- 格式规则验证（正则表达式）
- 必填字段验证
- 详细错误信息提示

### 环境配置

#### 数据库环境配置
- **test环境**: 使用Mock数据库，内存存储，快速测试
- **development环境**: 使用Mock数据库，便于开发调试
- **production环境**: 使用SQLite数据库，数据持久化

#### 环境变量
- `NODE_ENV`: 环境标识 (test/development/production)
- `PORT`: 服务端口 (默认3000)
- `DB_PATH`: 数据库文件路径 (生产环境)

#### 数据库初始化
```bash
# 初始化数据库表结构和示例数据
npm run init-db
```

## 部署架构
> 待Bob设计部署架构图

## 性能优化
> 待补充性能优化策略

## 安全考虑
> 待补充安全设计方案

## 监控与日志
> 待补充监控和日志方案

## 开发流程
1. 需求分析
2. 接口设计
3. 数据库设计
4. 编码实现
5. 单元测试
6. 集成测试
7. 部署上线

## 变更历史
| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| v1.0.0 | 2025-01-08 | 初始化文档结构 | Emma |
| v2.0.0 | 2025-08-07 | Sprint-2 数据模型扩展与优化 | Alex |

### v2.0.0 变更详情
- **数据库设计**: 完善了subjects、file_nodes、operation_logs表结构
- **数据模型**: 扩展Subject模型类，新增统计功能方法
- **数据验证**: 新增SubjectValidator验证工具类
- **双数据库支持**: 实现Mock数据库和SQLite数据库的智能切换
- **索引优化**: 创建学科名称唯一索引，优化查询性能
- **目录结构**: 更新项目目录结构，添加utils工具目录

## 待办事项

### 已完成 (Sprint-2)
- [x] 完善技术栈选型
- [x] 设计数据库结构
- [x] 扩展数据模型功能
- [x] 创建数据验证工具
- [x] 建立双数据库支持

### 待完成 (Sprint-2 后续)
- [ ] 实现后端API完整功能 (PUT /subjects/:id, GET /subjects/check-name)
- [ ] 创建控制器层和服务层
- [ ] 完善前端UI组件
- [ ] 端到端测试集成

### 长期待办
- [ ] 设计系统架构图
- [ ] 制定完整开发规范
- [ ] 设计部署方案
- [ ] 性能优化策略
- [ ] 安全设计方案
- [ ] 监控与日志方案

---

## Sprint-2 技术总结

### 数据模型扩展成果
1. **完整的学科数据模型**: 支持统计字段、实时统计计算、完整CRUD操作
2. **强大的数据验证体系**: 基于Joi的完整验证工具类，支持所有业务场景
3. **双数据库架构**: 灵活支持开发测试和生产环境的不同需求
4. **性能优化**: 通过索引优化和查询优化提升数据库性能

### 技术亮点
- **向后兼容**: 所有扩展都保持与现有API的完全兼容
- **测试驱动**: 完整的测试验证确保功能稳定性
- **模块化设计**: 清晰的分层架构，便于维护和扩展
- **错误处理**: 完善的异常处理和错误信息提示

### 下一步规划
基于当前的数据模型基础，Sprint-2后续将重点实现：
1. 后端API完整功能 (PUT更新接口、GET名称检查接口)
2. 前端UI完整实现 (学科卡片、编辑弹窗、实时验证)
3. 系统集成与端到端测试

**文档状态**: ✅ Sprint-2 数据模型扩展部分已完成
**最后更新**: 2025-08-07 by Alex (工程师)
