const Subject = require('../models/Subject');
const { subjectValidator } = require('../utils/subjectValidator');

/**
 * 学科业务逻辑服务层
 * 封装学科管理的复杂业务逻辑，提供统一的业务接口
 */
class SubjectService {
  constructor() {
    this.subjectModel = new Subject();
  }

  /**
   * 获取所有学科列表
   * @returns {Array} 学科列表
   */
  async getAllSubjects() {
    try {
      return this.subjectModel.findAll();
    } catch (error) {
      console.error('获取学科列表失败:', error);
      throw new Error('获取学科列表失败');
    }
  }

  /**
   * 根据ID获取学科详情
   * @param {number} id - 学科ID
   * @returns {Object|null} 学科详情
   */
  async getSubjectById(id) {
    try {
      // 验证ID
      const idValidation = subjectValidator.validateId(id);
      if (!idValidation.isValid) {
        throw new Error(idValidation.errors[0]);
      }

      const subject = this.subjectModel.findById(id);
      if (!subject) {
        throw new Error('学科不存在');
      }

      return subject;
    } catch (error) {
      console.error('获取学科详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建新学科
   * @param {Object} data - 学科数据
   * @returns {Object} 创建的学科信息
   */
  async createSubject(data) {
    try {
      // 验证数据
      const validation = subjectValidator.validateCreate(data);
      if (!validation.isValid) {
        throw new Error(validation.errors.join('; '));
      }

      // 检查名称唯一性
      const nameExists = !this.subjectModel.isNameAvailable(validation.data.name);
      if (nameExists) {
        throw new Error('学科名称已存在');
      }

      return this.subjectModel.create(validation.data);
    } catch (error) {
      console.error('创建学科失败:', error);
      throw error;
    }
  }

  /**
   * 更新学科信息
   * @param {number} id - 学科ID
   * @param {Object} data - 更新数据
   * @returns {Object} 更新后的学科信息
   */
  async updateSubject(id, data) {
    try {
      // 验证ID
      const idValidation = subjectValidator.validateId(id);
      if (!idValidation.isValid) {
        throw new Error(idValidation.errors[0]);
      }

      // 验证更新数据
      const validation = subjectValidator.validateUpdate(data);
      if (!validation.isValid) {
        throw new Error(validation.errors.join('; '));
      }

      // 检查学科是否存在
      const existingSubject = this.subjectModel.findById(id);
      if (!existingSubject) {
        throw new Error('学科不存在');
      }

      // 如果更新名称，检查名称唯一性
      if (validation.data.name && validation.data.name !== existingSubject.name) {
        const nameAvailable = this.subjectModel.isNameAvailable(validation.data.name, id);
        if (!nameAvailable) {
          throw new Error('学科名称已存在');
        }
      }

      return this.subjectModel.update(id, validation.data);
    } catch (error) {
      console.error('更新学科失败:', error);
      throw error;
    }
  }

  /**
   * 删除学科
   * @param {number} id - 学科ID
   * @returns {Object} 删除结果
   */
  async deleteSubject(id) {
    try {
      // 验证ID
      const idValidation = subjectValidator.validateId(id);
      if (!idValidation.isValid) {
        throw new Error(idValidation.errors[0]);
      }

      // 检查学科是否存在
      const existingSubject = this.subjectModel.findById(id);
      if (!existingSubject) {
        throw new Error('学科不存在');
      }

      return this.subjectModel.delete(id);
    } catch (error) {
      console.error('删除学科失败:', error);
      throw error;
    }
  }

  /**
   * 检查学科名称是否可用
   * @param {string} name - 学科名称
   * @param {number} excludeId - 排除的学科ID（可选）
   * @returns {Object} 检查结果
   */
  async checkNameAvailability(name, excludeId = null) {
    try {
      // 验证检查数据
      const validation = subjectValidator.validateCheckName({ name, excludeId });
      if (!validation.isValid) {
        throw new Error(validation.errors.join('; '));
      }

      const available = this.subjectModel.isNameAvailable(name, excludeId);

      const result = {
        name: name,
        available: available
      };

      // 如果名称不可用，找出冲突的学科ID
      if (!available) {
        const conflictSubject = this.subjectModel.findByName(name);
        if (conflictSubject && conflictSubject.id !== excludeId) {
          result.conflictId = conflictSubject.id;
        }
      }

      return result;
    } catch (error) {
      console.error('检查名称可用性失败:', error);
      throw error;
    }
  }

  /**
   * 获取学科统计信息
   * @param {number} id - 学科ID
   * @returns {Object} 统计信息
   */
  async getSubjectStats(id) {
    try {
      // 验证ID
      const idValidation = subjectValidator.validateId(id);
      if (!idValidation.isValid) {
        throw new Error(idValidation.errors[0]);
      }

      const stats = this.subjectModel.getStats(id);
      if (!stats) {
        throw new Error('学科不存在');
      }

      return stats;
    } catch (error) {
      console.error('获取学科统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新学科统计信息
   * @param {number} id - 学科ID
   * @returns {Object} 更新后的学科信息
   */
  async updateSubjectStats(id) {
    try {
      // 验证ID
      const idValidation = subjectValidator.validateId(id);
      if (!idValidation.isValid) {
        throw new Error(idValidation.errors[0]);
      }

      return this.subjectModel.updateStats(id);
    } catch (error) {
      console.error('更新学科统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新所有学科统计信息
   * @returns {Array} 更新后的学科列表
   */
  async updateAllSubjectsStats() {
    try {
      return this.subjectModel.updateAllStats();
    } catch (error) {
      console.error('批量更新学科统计信息失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const subjectService = new SubjectService();

module.exports = {
  SubjectService,
  subjectService
};
