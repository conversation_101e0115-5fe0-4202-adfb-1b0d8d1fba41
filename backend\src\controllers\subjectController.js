const { subjectService } = require('../services/subjectService');

/**
 * 学科管理控制器
 * 处理学科相关的HTTP请求和响应
 */
class SubjectController {
  /**
   * 获取所有学科列表
   */
  async getAllSubjects(ctx) {
    try {
      const subjects = await subjectService.getAllSubjects();

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取学科列表成功',
        data: subjects,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取学科列表失败:', error);

      ctx.status = 500;
      ctx.body = {
        success: false,
        code: 500,
        message: '获取学科列表失败',
        error: '数据库查询错误',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 根据ID获取学科详情
   */
  async getSubjectById(ctx) {
    try {
      const id = parseInt(ctx.params.id);

      if (isNaN(id) || id <= 0) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: '学科ID必须是正整数',
          timestamp: new Date().toISOString()
        };
        return;
      }

      const subject = await subjectService.getSubjectById(id);

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取学科详情成功',
        data: subject,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取学科详情失败:', error);

      if (error.message === '学科不存在') {
        ctx.status = 404;
        ctx.body = {
          success: false,
          code: 404,
          message: '学科不存在',
          error: `未找到ID为${ctx.params.id}的学科`,
          timestamp: new Date().toISOString()
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          code: 500,
          message: '获取学科详情失败',
          error: '数据库查询错误',
          timestamp: new Date().toISOString()
        };
      }
    }
  }

  /**
   * 创建新学科
   */
  async createSubject(ctx) {
    try {
      const { name, description } = ctx.request.body;
      const newSubject = await subjectService.createSubject({ name, description });

      ctx.status = 201;
      ctx.body = {
        success: true,
        code: 201,
        message: '学科创建成功',
        data: newSubject,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('创建学科失败:', error);

      if (error.message === '学科名称已存在') {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: '学科名称已存在',
          timestamp: new Date().toISOString()
        };
      } else if (error.message.includes('验证失败') || error.message.includes('不能为空') || error.message.includes('格式')) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数验证失败',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          code: 500,
          message: '创建学科失败',
          error: '数据库操作错误',
          timestamp: new Date().toISOString()
        };
      }
    }
  }

  /**
   * 更新学科信息
   */
  async updateSubject(ctx) {
    try {
      const id = parseInt(ctx.params.id);

      if (isNaN(id) || id <= 0) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: '学科ID必须是正整数',
          timestamp: new Date().toISOString()
        };
        return;
      }

      const { name, description } = ctx.request.body;
      const updatedSubject = await subjectService.updateSubject(id, { name, description });

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '学科更新成功',
        data: updatedSubject,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('更新学科失败:', error);

      if (error.message === '学科不存在') {
        ctx.status = 404;
        ctx.body = {
          success: false,
          code: 404,
          message: '学科不存在',
          error: `未找到ID为${ctx.params.id}的学科`,
          timestamp: new Date().toISOString()
        };
      } else if (error.message === '学科名称已存在') {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: '学科名称已存在',
          timestamp: new Date().toISOString()
        };
      } else if (error.message.includes('验证失败') || error.message.includes('至少需要提供') || error.message.includes('格式')) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数验证失败',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          code: 500,
          message: '更新学科失败',
          error: '数据库操作错误',
          timestamp: new Date().toISOString()
        };
      }
    }
  }

  /**
   * 删除学科
   */
  async deleteSubject(ctx) {
    try {
      const id = parseInt(ctx.params.id);

      if (isNaN(id) || id <= 0) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: '学科ID必须是正整数',
          timestamp: new Date().toISOString()
        };
        return;
      }

      const result = await subjectService.deleteSubject(id);

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '学科删除成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('删除学科失败:', error);

      if (error.message === '学科不存在') {
        ctx.status = 404;
        ctx.body = {
          success: false,
          code: 404,
          message: '学科不存在',
          error: `未找到ID为${ctx.params.id}的学科`,
          timestamp: new Date().toISOString()
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          code: 500,
          message: '删除学科失败',
          error: '数据库操作错误',
          timestamp: new Date().toISOString()
        };
      }
    }
  }

  /**
   * 检查学科名称是否可用
   */
  async checkNameAvailability(ctx) {
    try {
      const { name, excludeId } = ctx.query;

      if (!name) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: '学科名称不能为空',
          timestamp: new Date().toISOString()
        };
        return;
      }

      const excludeIdNum = excludeId && !isNaN(parseInt(excludeId)) ? parseInt(excludeId) : null;
      const result = await subjectService.checkNameAvailability(name, excludeIdNum);

      const message = result.available ? '学科名称可用' : '学科名称不可用';

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: message,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('检查名称可用性失败:', error);

      if (error.message.includes('验证失败') || error.message.includes('不能为空') || error.message.includes('格式')) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          code: 400,
          message: '请求参数错误',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          code: 500,
          message: '检查名称可用性失败',
          error: '数据库查询错误',
          timestamp: new Date().toISOString()
        };
      }
    }
  }
}

// 创建单例实例
const subjectController = new SubjectController();

module.exports = {
  SubjectController,
  subjectController
};
