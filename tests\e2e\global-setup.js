// global-setup.js
const { chromium } = require('@playwright/test');

async function globalSetup() {
  console.log('🚀 Starting global setup for E2E tests...');
  
  // 等待服务器启动
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // 等待后端服务器启动
    console.log('⏳ Waiting for backend server...');
    let backendReady = false;
    let attempts = 0;
    const maxAttempts = 30;
    
    while (!backendReady && attempts < maxAttempts) {
      try {
        const response = await page.goto('http://localhost:3000/api/v1/health', { 
          waitUntil: 'networkidle',
          timeout: 2000 
        });
        if (response && response.ok()) {
          backendReady = true;
          console.log('✅ Backend server is ready');
        }
      } catch (error) {
        attempts++;
        console.log(`⏳ Backend not ready, attempt ${attempts}/${maxAttempts}`);
        await page.waitForTimeout(2000);
      }
    }
    
    if (!backendReady) {
      throw new Error('Backend server failed to start within timeout');
    }
    
    // 等待前端服务器启动
    console.log('⏳ Waiting for frontend server...');
    let frontendReady = false;
    attempts = 0;
    
    while (!frontendReady && attempts < maxAttempts) {
      try {
        const response = await page.goto('http://localhost:5173', { 
          waitUntil: 'networkidle',
          timeout: 2000 
        });
        if (response && response.ok()) {
          frontendReady = true;
          console.log('✅ Frontend server is ready');
        }
      } catch (error) {
        attempts++;
        console.log(`⏳ Frontend not ready, attempt ${attempts}/${maxAttempts}`);
        await page.waitForTimeout(2000);
      }
    }
    
    if (!frontendReady) {
      throw new Error('Frontend server failed to start within timeout');
    }
    
    // 初始化数据库
    console.log('🗄️ Initializing database...');
    try {
      await page.goto('http://localhost:5173/admin');
      await page.waitForSelector('button:has-text("初始化数据库")', { timeout: 10000 });
      await page.click('button:has-text("初始化数据库")');
      await page.waitForTimeout(2000);
      console.log('✅ Database initialized');
    } catch (error) {
      console.log('⚠️ Database initialization failed, continuing...');
    }
    
    console.log('✅ Global setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

module.exports = globalSetup;
