import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// API响应接口定义
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: string
  error?: string
}

// 学科接口定义
export interface Subject {
  id: number
  name: string
  description?: string
  file_count?: number
  total_size?: number
  created_at: string
  updated_at: string
}

export interface CreateSubjectRequest {
  name: string
  description?: string
}

export interface UpdateSubjectRequest {
  name?: string
  description?: string
}

export interface CheckNameResponse {
  name: string
  available: boolean
  conflictId?: number
}

// 健康检查接口定义
export interface HealthStatus {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  version: string
  database: 'connected' | 'disconnected'
  response_time_ms: number
  environment: string
}

// 创建axios实例
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: '/api/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      console.log(`→ ${config.method?.toUpperCase()} ${config.url}`)
      return config
    },
    (error) => {
      console.error('请求拦截器错误:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      console.log(`← ${response.config.method?.toUpperCase()} ${response.config.url} ${response.status}`)
      return response
    },
    (error) => {
      console.error('响应拦截器错误:', error)

      // 处理网络错误
      if (!error.response) {
        console.error('网络错误或服务器无响应')
        return Promise.reject({
          success: false,
          code: 0,
          message: '网络错误',
          error: '无法连接到服务器',
          timestamp: new Date().toISOString()
        })
      }

      // 返回服务器错误响应
      return Promise.reject(error.response.data)
    }
  )

  return client
}

// 创建API客户端实例
const apiClient = createApiClient()

// API服务类
export class ApiService {
  // 健康检查
  static async checkHealth(): Promise<ApiResponse<HealthStatus>> {
    const response = await apiClient.get<ApiResponse<HealthStatus>>('/health')
    return response.data
  }

  // 获取所有学科
  static async getSubjects(): Promise<ApiResponse<Subject[]>> {
    const response = await apiClient.get<ApiResponse<Subject[]>>('/subjects')
    return response.data
  }

  // 获取学科详情
  static async getSubject(id: number): Promise<ApiResponse<Subject>> {
    const response = await apiClient.get<ApiResponse<Subject>>(`/subjects/${id}`)
    return response.data
  }

  // 创建学科
  static async createSubject(data: CreateSubjectRequest): Promise<ApiResponse<Subject>> {
    const response = await apiClient.post<ApiResponse<Subject>>('/subjects', data)
    return response.data
  }

  // 更新学科
  static async updateSubject(id: number, data: UpdateSubjectRequest): Promise<ApiResponse<Subject>> {
    const response = await apiClient.put<ApiResponse<Subject>>(`/subjects/${id}`, data)
    return response.data
  }

  // 检查学科名称是否可用
  static async checkSubjectName(name: string, excludeId?: number): Promise<ApiResponse<CheckNameResponse>> {
    const params = new URLSearchParams({ name })
    if (excludeId !== undefined) {
      params.append('excludeId', excludeId.toString())
    }
    const response = await apiClient.get<ApiResponse<CheckNameResponse>>(`/subjects/check-name?${params}`)
    return response.data
  }

  // 删除学科
  static async deleteSubject(id: number): Promise<ApiResponse<{ deleted_id: number; deleted_files_count: number }>> {
    const response = await apiClient.delete<ApiResponse<{ deleted_id: number; deleted_files_count: number }>>(`/subjects/${id}`)
    return response.data
  }

  // 初始化数据库 (开发专用)
  static async initDatabase(): Promise<ApiResponse<any>> {
    const response = await apiClient.post<ApiResponse<any>>('/dev/init-database')
    return response.data
  }

  // 获取数据库状态 (开发专用)
  static async getDatabaseStatus(): Promise<ApiResponse<any>> {
    const response = await apiClient.get<ApiResponse<any>>('/dev/database-status')
    return response.data
  }
}

export default apiClient
