# Sprint-2 学科管理完整功能 - 任务规划文档

## 文档信息
- **版本**: v1.0
- **创建时间**: 2025-01-08
- **负责人**: Emma (产品经理)
- **项目**: 期末复习平台
- **Sprint**: Sprint-2

## 项目背景与目标

基于Sprint-2详细计划文档，将"学科管理完整功能"切片转化为结构化的任务管理体系。实现学科的创建、查看、编辑、删除功能，包括完整的前端界面和后端API，支持学科名称唯一性验证、长度限制、删除二次确认等业务规则。

### 核心设计原则
1. **API契约优先**: 所有开发工作以API契约为"法律合同"
2. **测试驱动开发**: 每个功能都必须有完整的测试覆盖
3. **用户体验优先**: 提供清晰的操作反馈和错误提示
4. **渐进式增强**: 在现有架构基础上进行扩展，而非重写

## 任务分解结果

### 任务1: API契约设计与评审
**任务ID**: `62fcfa76-7dc1-48e2-bdfd-f425af822c6a`
**优先级**: P0 (最高)
**预计工期**: 1-2个工作日

**任务描述**:
基于现有API基础，补充完善学科管理的完整API契约，包括更新接口、名称唯一性检查接口等缺失功能。确保API契约成为前后端协作的'法律合同'，所有后续开发严格按照契约执行。

**实施指南**:
1. 分析现有API接口(/api/v1/subjects的GET、POST、DELETE)
2. 补充缺失的PUT /api/v1/subjects/:id更新接口
3. 添加GET /api/v1/subjects/check-name名称检查接口
4. 扩展响应数据结构，添加file_count、total_size统计字段
5. 完善错误响应格式，包括详细的错误码和错误信息
6. 更新API_Reference.md文档，冻结API契约

**验收标准**:
- API契约文档完整准确，包含所有接口的详细规格
- 响应格式和错误处理标准明确定义
- 数据验证规则清晰
- 所有相关人员理解并同意遵守契约

**相关文件**:
- `docs/architecture/API_Reference.md` (TO_MODIFY)
- `backend/src/routes/subjects.js` (REFERENCE)

---

### 任务2: 数据模型扩展与优化
**任务ID**: `f44df304-74ef-4656-9846-195627a14673`
**优先级**: P0 (最高)
**预计工期**: 1-2个工作日
**依赖任务**: API契约设计与评审

**任务描述**:
基于现有Subject模型，扩展数据库表结构以支持统计字段，优化查询性能，确保数据模型完全支持学科管理的完整功能需求。

**实施指南**:
1. 分析现有Subject.js模型类的实现
2. 扩展subjects表结构，添加file_count、total_size字段
3. 创建name字段的唯一索引优化查询性能
4. 扩展Subject模型类，添加统计信息查询方法
5. 实现数据验证工具类，支持业务规则验证
6. 更新数据库初始化脚本

**验收标准**:
- 数据库表结构支持所有业务需求
- Subject模型类支持完整的CRUD操作
- 数据验证工具实现完成
- 数据库索引创建完成，查询性能优化

**相关文件**:
- `backend/src/models/Subject.js` (TO_MODIFY)
- `backend/src/config/database.js` (TO_MODIFY)
- `backend/src/utils/subjectValidator.js` (CREATE)

---

### 任务3: 后端API完整实现
**任务ID**: `c9d56909-18f5-4e49-8b17-be9f73aac96d`
**优先级**: P0 (最高)
**预计工期**: 2-3个工作日
**依赖任务**: 数据模型扩展与优化

**任务描述**:
基于现有路由和控制器，实现学科管理的完整后端API功能，包括更新接口、名称检查接口、统计信息接口等。采用TDD开发模式，确保代码质量和业务逻辑正确性。

**实施指南**:
1. 扩展现有subjects.js路由，添加PUT /:id和GET /check-name路由
2. 创建SubjectService业务逻辑层，封装复杂业务逻辑
3. 创建SubjectController控制器层，处理HTTP请求响应
4. 复用现有的validation.js中间件，扩展验证规则
5. 编写完整的单元测试和集成测试
6. 确保所有API响应格式与契约100%一致

**验收标准**:
- 所有学科管理API接口按照契约实现完成
- 测试覆盖率>90%
- API行为与契约100%一致
- 业务规则验证正确实现
- 错误处理和状态码符合契约规范

**相关文件**:
- `backend/src/routes/subjects.js` (TO_MODIFY)
- `backend/src/controllers/subjectController.js` (CREATE)
- `backend/src/services/subjectService.js` (CREATE)
- `backend/src/middleware/validation.js` (TO_MODIFY)
- `backend/tests/integration/subjects.test.js` (TO_MODIFY)

---

### 任务4: 前端UI完整实现
**任务ID**: `7c3601ff-2f37-4100-b0cb-bec16def8d90`
**优先级**: P1 (高)
**预计工期**: 2-3个工作日
**依赖任务**: 后端API完整实现

**任务描述**:
基于现有AdminPanel组件，实现学科管理的完整前端界面，包括学科卡片组件、创建编辑弹窗、实时验证等功能。确保用户体验优秀，操作反馈清晰。

**实施指南**:
1. 扩展现有api.ts服务层，添加update和checkName方法
2. 创建SubjectCard.vue学科卡片组件，支持编辑删除操作
3. 创建SubjectModal.vue弹窗组件，支持创建和编辑模式
4. 扩展AdminPanel.vue，集成新组件，实现完整管理界面
5. 实现实时名称唯一性验证
6. 添加响应式设计，适配多种设备
7. 编写组件测试用例

**验收标准**:
- 学科管理界面实现完成
- API服务层与后端API完全对应
- 组件测试覆盖率>80%
- 用户体验优秀，操作反馈清晰
- 响应式设计适配多种设备
- 错误处理和用户反馈机制完善

**相关文件**:
- `frontend/src/services/api.ts` (TO_MODIFY)
- `frontend/src/components/SubjectCard.vue` (CREATE)
- `frontend/src/components/SubjectModal.vue` (CREATE)
- `frontend/src/views/AdminPanel.vue` (TO_MODIFY)
- `frontend/src/components/__tests__/SubjectCard.test.ts` (CREATE)
- `frontend/src/components/__tests__/SubjectModal.test.ts` (CREATE)

---

### 任务5: 系统集成与E2E测试
**任务ID**: `c19b4d1d-ae92-4dc7-b6e0-86822a4200f0`
**优先级**: P1 (高)
**预计工期**: 1-2个工作日
**依赖任务**: 前端UI完整实现

**任务描述**:
进行前后端完整联调，使用Playwright进行端到端测试，模拟真实用户的学科管理操作流程。确保整个系统功能完整，用户体验流畅。

**实施指南**:
1. 配置前后端联调环境，确保数据传输正常
2. 使用Playwright编写E2E测试用例，覆盖完整用户流程
3. 测试学科创建、编辑、删除的完整操作流程
4. 测试表单验证、错误处理、用户反馈机制
5. 测试响应式设计在不同设备上的表现
6. 进行性能测试，确保API响应时间<3秒
7. 修复集成过程中发现的问题

**验收标准**:
- 前后端联调成功，数据传输正常
- 端到端测试100%通过
- 所有API接口响应时间<3秒
- 页面加载时间<5秒
- 错误处理机制完善
- 响应式设计在各种设备上正常工作

**相关文件**:
- `tests/e2e/subject-management.spec.js` (CREATE)
- `tests/e2e/performance.spec.js` (CREATE)
- `playwright.config.js` (TO_MODIFY)
- `package.json` (TO_MODIFY)
- `docs/CHANGELOG.md` (TO_MODIFY)

## 依赖关系图

```
任务1: API契约设计与评审
    ↓
任务2: 数据模型扩展与优化
    ↓
任务3: 后端API完整实现
    ↓
任务4: 前端UI完整实现
    ↓
任务5: 系统集成与E2E测试
```

## 关键里程碑

1. **API契约冻结** (任务1完成) - 前后端开发的基础
2. **数据模型就绪** (任务2完成) - 支持完整业务功能
3. **后端API完成** (任务3完成) - 前端开发可以开始
4. **前端UI完成** (任务4完成) - 功能开发完成
5. **系统集成完成** (任务5完成) - Sprint-2交付就绪

## 质量保证

### 测试覆盖率要求
- 后端单元测试: >90%
- 后端集成测试: >85%
- 前端组件测试: >80%
- E2E测试: 100%覆盖核心用户流程

### 性能要求
- API响应时间: <3秒
- 页面加载时间: <5秒
- 数据库查询优化: 添加必要索引

### 用户体验要求
- 响应式设计: 适配桌面、平板、手机
- 操作反馈: 清晰的成功/错误提示
- 表单验证: 实时验证，友好的错误信息

## 风险评估与缓解

### 高风险项
1. **API契约变更风险** - 缓解: 严格评审流程，一旦冻结不允许变更
2. **数据库迁移风险** - 缓解: 充分测试，准备回滚方案
3. **前后端集成风险** - 缓解: 早期联调，持续集成

### 中风险项
1. **测试覆盖率不足** - 缓解: TDD开发模式，代码审查
2. **性能不达标** - 缓解: 性能测试，数据库优化
3. **用户体验问题** - 缓解: 原型验证，用户反馈

## 交付计划

### Sprint-2总体时间线: 7-10个工作日

**第1-2天**: 任务1 (API契约设计与评审)
**第3-4天**: 任务2 (数据模型扩展与优化)
**第5-7天**: 任务3 (后端API完整实现)
**第8-10天**: 任务4 (前端UI完整实现)
**第11-12天**: 任务5 (系统集成与E2E测试)

### 并行执行机会
- 任务3和任务4可以部分并行(API完成后前端可以开始)
- 测试用例编写可以与功能开发并行进行

## 成功标准

Sprint-2成功交付的标准:
1. ✅ 所有5个任务100%完成
2. ✅ 测试覆盖率达到要求
3. ✅ 性能指标达标
4. ✅ 用户体验验收通过
5. ✅ 代码质量审查通过
6. ✅ 文档更新完成

---

**文档状态**: ✅ 已完成
**下一步行动**: 等待Mike批准，开始任务执行